"""
警情数据控制器
简化的主控制器，协调各个服务
"""

from typing import Dict, Any, Optional
import pandas as pd
from datetime import datetime

from services.service_factory import ServiceFactory
from services.session_service import SessionService
from services.intent_service import IntentService
from services.data_query_service import DataQueryService
from services.llm_service import LLMService
from services.professional_report_service import ProfessionalReportService
from services.police_tag_service import PoliceTagService
from core.exceptions import BusinessLogicError, ResourceNotFoundError
from core.logging_config import LoggerMixin, log_performance


class PoliceDataController(LoggerMixin):
    """警情数据控制器"""
    
    def __init__(self, service_factory: ServiceFactory):
        self.service_factory = service_factory
        self.session_service: SessionService = service_factory.get_session_service()
        self.intent_service: IntentService = service_factory.get_intent_service()
        self.data_query_service: DataQueryService = service_factory.get_data_query_service()
        self.llm_service: LLMService = service_factory.get_llm_service()
        self.police_tag_service: PoliceTagService = service_factory.get_tag_service()
        self.professional_report_service: ProfessionalReportService = ProfessionalReportService(
            self.llm_service,
            service_factory.get_db_manager(),
            self.police_tag_service
        )
    
    @log_performance("处理结构化查询")
    def process_structured_query(
        self,
        session_id: str,
        display_text: str,
        query_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理智能标签选择器的结构化查询"""
        try:
            # 1. 验证会话
            session_result = self.session_service.get_session(session_id)
            if not session_result['success']:
                raise ResourceNotFoundError(f"会话不存在: {session_id}")

            session = session_result['session']

            # 2. 记录用户消息到会话历史
            self.session_service.add_conversation(session_id, 'user', display_text)

            # 3. 构建意图信息
            intent_info = self._build_intent_from_params(query_params)

            # 4. 执行数据查询
            query_result = self.data_query_service.process_query(
                user_message=display_text,
                intent_info=intent_info,
                for_report=False
            )

            if not query_result['success']:
                error_message = f"查询失败: {query_result.get('message', '未知错误')}"
                self.session_service.add_conversation(session_id, 'assistant', error_message)
                return {
                    'success': False,
                    'message': error_message
                }

            # 5. 构建响应
            data_list = query_result.get('data', [])
            total_records = query_result.get('total_records', 0)

            response_message = f"查询完成，共找到 {total_records} 条记录"

            # 6. 记录助手回复到会话历史
            self.session_service.add_conversation(session_id, 'assistant', response_message)

            # 6.5. 保存查询结果到会话上下文中，以便后续生成报告使用
            if total_records > 0:
                try:
                    # 构建query_params用于后续的Excel导出和报告生成
                    query_params_for_context = {
                        'time_range': query_result.get('extracted_info', {}).get('time_range'),
                        'location': query_result.get('extracted_info', {}).get('location', {}),
                        'situation_type': query_result.get('extracted_info', {}).get('situation_type', []),
                        'place_occur': query_result.get('extracted_info', {}).get('place_occur', []),
                        'place_category': query_result.get('extracted_info', {}).get('place_category', [])
                    }

                    # 记录查询时间戳用于调试
                    current_timestamp = datetime.now().isoformat()
                    self.logger.info(f"结构化查询-更新会话上下文，查询时间戳: {current_timestamp}, 记录数: {total_records}")

                    self.session_service.update_session(
                        session_id=session_id or '',
                        context={
                            'last_query_result': {
                                'data': data_list,
                                'query_info': query_result.get('extracted_info', {}),
                                'query_params': query_params_for_context,
                                'timestamp': current_timestamp,
                                'total_records': total_records
                            }
                        }
                    )
                except Exception as e:
                    self.logger.warning(f"结构化查询-保存查询结果到会话上下文失败: {str(e)}")

            # 7. 返回结果（只返回预览数据，避免大量数据导致前端无响应）
            preview_data = data_list[:10] if isinstance(data_list, list) and len(data_list) > 0 else []
            result = {
                'success': True,
                'message': response_message,
                'total_records': total_records,
                'data': preview_data,  # 只返回预览数据
                'preview_data': preview_data,
                'has_more_data': total_records > 10,
                'full_data_available': total_records > 0,  # 标识有全量数据可获取
                'query_params': query_params,  # 保存查询参数用于后续操作
                'display_text': display_text
            }

            self.logger.info(f"结构化查询完成: {total_records} 条记录")
            return result

        except Exception as e:
            error_message = f"结构化查询处理失败: {str(e)}"
            self.logger.error(error_message)

            # 记录错误到会话历史
            try:
                self.session_service.add_conversation(session_id, 'assistant', error_message)
            except:
                pass

            return {
                'success': False,
                'message': error_message
            }

    @log_performance("获取全量数据")
    def get_full_data(
        self,
        session_id: str,
        query_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取全量数据（用于导出等操作）"""
        try:
            # 1. 验证会话
            session_result = self.session_service.get_session(session_id)
            if not session_result['success']:
                raise ResourceNotFoundError(f"会话不存在: {session_id}")

            # 2. 构建意图信息
            intent_info = self._build_intent_from_params(query_params)

            # 3. 执行数据查询（获取全量数据）
            # 确保使用标准字段进行导出，强制指定字段列表
            intent_info['force_standard_fields'] = True

            # 直接指定导出字段，确保不包含create_time，使用county而不是county_feedback
            export_fields = [
                'id', 'jjdbh', 'bjsj', 'county', 'town', 'community',
                'police_situation_category', 'police_situation_type', 'cause',
                'place_category', 'place_main_category', 'place_name', 'place_remark', 'cjqk'
            ]
            intent_info['export_fields'] = export_fields

            query_result = self.data_query_service.process_query(
                user_message="获取全量数据用于导出",
                intent_info=intent_info,
                for_report=False
            )

            if not query_result.get('success', False):
                raise DataProcessingError(f"查询失败: {query_result.get('message', '未知错误')}")

            # 4. 返回全量数据
            data_list = query_result.get('data', [])
            total_records = query_result.get('total_records', 0)

            result = {
                'success': True,
                'message': f'获取全量数据完成，共 {total_records} 条记录',
                'total_records': total_records,
                'data': data_list,
                'query_params': query_params
            }

            self.logger.info(f"获取全量数据完成: {total_records} 条记录")
            return result

        except Exception as e:
            error_message = f"获取全量数据失败: {str(e)}"
            self.logger.error(error_message)
            return {
                'success': False,
                'message': error_message
            }

    @log_performance("导出Excel数据")
    def export_data_to_excel(
        self,
        session_id: str,
        query_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """导出数据为Excel格式"""
        try:
            # 1. 获取全量数据
            full_data_result = self.get_full_data(session_id, query_params)
            if not full_data_result.get('success', False):
                return full_data_result

            data_list = full_data_result.get('data', [])
            if not data_list:
                return {
                    'success': False,
                    'message': '没有数据可导出'
                }

            # 2. 导出为Excel
            from services.excel_export_service import ExcelExportService
            excel_service = ExcelExportService()

            export_result = excel_service.export_police_data_to_excel(data_list)

            if export_result.get('success', False):
                self.logger.info(f"Excel导出完成: {len(data_list)} 条记录")

            return export_result

        except Exception as e:
            error_message = f"Excel导出失败: {str(e)}"
            self.logger.error(error_message)
            return {
                'success': False,
                'message': error_message
            }

    def _build_intent_from_params(self, query_params: Dict[str, Any]) -> Dict[str, Any]:
        """从查询参数构建意图信息"""
        intent_info = {}

        # 处理时间范围
        if query_params.get('time_range'):
            intent_info['time_range'] = query_params['time_range']

        # 处理地点信息
        if query_params.get('location'):
            intent_info['location'] = query_params['location']

        # 处理警情类型
        if query_params.get('situation_type'):
            intent_info['situation_type'] = query_params['situation_type']

        # 处理发生处所
        if query_params.get('place_occur'):
            intent_info['place_occur'] = query_params['place_occur']

        # 处理场所类型
        if query_params.get('place_category'):
            intent_info['place_category'] = query_params['place_category']

        return intent_info

    @log_performance("处理用户消息")
    def process_message(
        self,
        session_id: str,
        user_message: str,
        query_params: Dict[str, Any] = None,
        for_report: bool = False
    ) -> Dict[str, Any]:
        """处理用户消息的主入口"""
        try:
            # 1. 验证会话
            session_result = self.session_service.get_session(session_id)
            if not session_result['success']:
                raise ResourceNotFoundError(f"会话不存在: {session_id}")

            session = session_result['session']
            
            # 2. 添加用户消息到对话历史
            self.session_service.add_conversation(
                session_id=session_id,
                role='user',
                content=user_message
            )
            
            # 3. 获取对话上下文
            context = self.session_service.get_context_from_history(session_id)
            
            # 4. 分析用户意图
            intent_result = self.intent_service.analyze_intent(
                user_message=user_message,
                context=context,
                current_intent_info=session['intent_info']
            )

            # 检查意图分析是否成功
            if not intent_result.get('success', False):
                raise Exception(f"意图分析失败: {intent_result.get('message', '未知错误')}")

            # 确保intent_result包含必需的字段
            if 'updated_intent_info' not in intent_result:
                self.logger.warning(f"意图分析结果缺少updated_intent_info字段: {intent_result}")
                intent_result['updated_intent_info'] = intent_result.get('entities', {})

            if 'intent_type' not in intent_result:
                self.logger.warning(f"意图分析结果缺少intent_type字段: {intent_result}")
                intent_result['intent_type'] = 'general'

            if 'entities' not in intent_result:
                self.logger.warning(f"意图分析结果缺少entities字段: {intent_result}")
                intent_result['entities'] = {}

            # 5. 更新会话的意图信息
            self.session_service.update_session(
                session_id=session_id,
                intent_info=intent_result['updated_intent_info']
            )
            
            # 6. 根据意图类型处理请求
            self.logger.info(f"意图分析结果: intent_type={intent_result.get('intent_type')}, entities={intent_result.get('entities')}")
            response = self._handle_intent(
                intent_result=intent_result,
                session_id=session_id,
                user_message=user_message,
                query_params=query_params,
                for_report=for_report
            )
            # 避免在日志中打印完整的报告内容
            response_summary = response.copy()
            if 'report_content' in response_summary:
                response_summary['report_content'] = '[报告内容已生成，长度: {}]'.format(
                    len(str(response_summary['report_content']))
                )
            self.logger.info(f"意图处理结果: {response_summary}")
            
            # 7. 添加系统回复到对话历史
            # 如果是报告生成响应，需要包含按钮标记以便前端重新显示按钮
            conversation_content = response.get('message', '')
            if response.get('report_content'):
                # 使用响应中已有的报告ID，如果没有则生成新的
                report_id = response.get('report_id')
                if not report_id:
                    import time
                    report_id = f'report_{int(time.time() * 1000)}'
                    response['report_id'] = report_id
                # 添加按钮标记到消息内容中
                conversation_content += f'\n\n**[REPORT_BUTTON:{report_id}]**'

            self.session_service.add_conversation(
                session_id=session_id,
                role='assistant',
                content=conversation_content,
                metadata={
                    'intent_type': intent_result['intent_type'],
                    'confidence_score': intent_result['confidence_score'],
                    'has_report': bool(response.get('report_content')),
                    'report_id': response.get('report_id')
                }
            )
            
            return response
            
        except Exception as e:
            self.log_error(e, "处理用户消息")
            error_response = {
                'success': False,
                'message': f'处理消息时出现错误: {str(e)}',
                'error_type': type(e).__name__
            }
            
            # 尝试添加错误消息到对话历史
            try:
                self.session_service.add_conversation(
                    session_id=session_id,
                    role='assistant',
                    content=error_response['message'],
                    metadata={'error': True}
                )
            except:
                pass  # 忽略添加对话历史的错误
            
            return error_response
    
    def _handle_intent(
        self,
        intent_result: Dict[str, Any],
        session_id: str,
        user_message: str,
        query_params: Dict[str, Any] = None,
        for_report: bool = False
    ) -> Dict[str, Any]:
        """根据意图类型处理请求"""
        intent_type = intent_result['intent_type']
        entities = intent_result['entities']

        self.logger.info(f"处理意图: {intent_type}, 用户消息: {user_message}")

        if intent_type == 'data_query':
            self.logger.info("执行数据查询处理")
            return self._handle_data_query(entities, user_message, session_id)

        elif intent_type == 'report_generation':
            self.logger.info("执行报告生成处理")
            return self._handle_report_generation(entities, user_message, session_id, query_params)

        elif intent_type == 'data_visualization':
            self.logger.info("执行数据可视化处理")
            return self._handle_data_visualization(entities, user_message)

        elif intent_type == 'greeting':
            self.logger.info("执行问候处理")
            return self._handle_greeting()

        elif intent_type == 'farewell':
            self.logger.info("执行告别处理")
            return self._handle_farewell()

        else:
            self.logger.info(f"执行一般问题处理，意图类型: {intent_type}")
            return self._handle_general_question(user_message, entities)
    
    def _handle_data_query(
        self,
        entities: Dict[str, Any],
        user_message: str,
        session_id: str = None
    ) -> Dict[str, Any]:
        """处理数据查询请求"""
        try:
            # 使用数据查询服务处理请求
            query_result = self.data_query_service.process_query(
                user_message=user_message,
                intent_info=entities
            )
            
            if query_result['success']:
                data_list = query_result['data']
                total_records = query_result['total_records']

                if total_records == 0:
                    message = "根据您的查询条件，未找到匹配的数据。请尝试调整查询条件。"
                else:
                    message = f"查询完成！共找到 {total_records} 条记录。"

                    # 添加数据摘要
                    # 安全地检查数据是否存在
                    has_data_for_summary = False
                    if isinstance(data_list, list):
                        has_data_for_summary = len(data_list) > 0
                    elif hasattr(data_list, '__len__'):
                        try:
                            has_data_for_summary = len(data_list) > 0
                        except:
                            has_data_for_summary = False
                    elif hasattr(data_list, 'empty'):
                        has_data_for_summary = not data_list.empty

                    if has_data_for_summary:
                        # 将字典列表转换为DataFrame以便分析
                        import pandas as pd
                        data_df = pd.DataFrame(data_list)
                        summary = self._generate_data_summary(data_df)
                        message += f"\n\n{summary}"

                # 安全地检查数据是否存在
                has_data = False
                if isinstance(data_list, list):
                    has_data = len(data_list) > 0
                elif hasattr(data_list, '__len__'):
                    has_data = len(data_list) > 0
                elif hasattr(data_list, 'empty'):
                    has_data = not data_list.empty

                self.logger.info(f"数据检查结果: has_data={has_data}, data_list类型={type(data_list)}, 长度={len(data_list) if hasattr(data_list, '__len__') else 'N/A'}")

                # 将查询结果保存到会话上下文中，以便后续生成报告使用
                if has_data:
                    try:
                        # 构建query_params用于后续的Excel导出和报告生成
                        query_params_for_context = {
                            'time_range': query_result.get('extracted_info', {}).get('time_range'),
                            'location': query_result.get('extracted_info', {}).get('location', {}),
                            'situation_type': query_result.get('extracted_info', {}).get('situation_type', []),
                            'place_occur': query_result.get('extracted_info', {}).get('place_occur', []),
                            'place_category': query_result.get('extracted_info', {}).get('place_category', [])
                        }

                        # 记录查询时间戳用于调试
                        current_timestamp = datetime.now().isoformat()
                        self.logger.info(f"更新会话上下文，查询时间戳: {current_timestamp}, 记录数: {total_records}")

                        self.session_service.update_session(
                            session_id=session_id or '',
                            context={
                                'last_query_result': {
                                    'data': data_list,
                                    'query_info': query_result.get('extracted_info', {}),
                                    'query_params': query_params_for_context,
                                    'timestamp': current_timestamp,
                                    'total_records': total_records
                                }
                            }
                        )
                    except Exception as e:
                        self.logger.warning(f"保存查询结果到会话上下文失败: {str(e)}")

                return {
                    'success': True,
                    'message': message,
                    'data_summary': self._format_data_for_display(data_list) if has_data else None,
                    'total_records': total_records,
                    'query_info': query_result.get('extracted_info', {})
                }
            else:
                return query_result
                
        except Exception as e:
            self.log_error(e, "处理数据查询")
            return {
                'success': False,
                'message': f'数据查询失败: {str(e)}'
            }
    
    def _handle_report_generation(
        self,
        entities: Dict[str, Any],
        user_message: str,
        session_id: str,
        query_params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理报告生成请求"""
        try:
            # 检查是否可以基于会话历史中的查询结果生成报告
            session_result = self.session_service.get_session(session_id)
            if session_result['success']:
                session_data = session_result['session']
                # 检查会话上下文中是否有最近的查询结果
                recent_query_data = session_data.get('context', {}).get('last_query_result')

                # 调试日志：显示缓存的查询结果信息
                if recent_query_data:
                    self.logger.info(f"找到缓存的查询结果，时间戳: {recent_query_data.get('timestamp')}, 记录数: {recent_query_data.get('total_records')}")
                else:
                    self.logger.info("未找到缓存的查询结果")

                # 如果有传入的query_params或者用户明确要求基于"这些数据"生成报告，优先使用暂存的查询结果
                if (recent_query_data and (
                    query_params or  # 有传入的query_params说明是点击按钮生成报告
                    any(keyword in user_message.lower() for keyword in
                        ['这些数据', '刚才的', '上面的', '之前的', '刚查询的', '刚才查询的', '分析以上数据'])
                )):
                    self.logger.info("基于会话历史中的查询结果生成报告")
                    return self._generate_report_from_cached_data(
                        recent_query_data, user_message, session_id
                    )

            # 否则，重新查询数据生成报告
            # 如果有传入的query_params，直接使用它们查询数据
            if query_params:
                self.logger.info("使用传入的query_params生成报告")
                query_result = self.get_full_data(session_id, query_params)
            else:
                # 为报告生成添加特殊标记，使用优化的字段选择
                if entities:
                    entities['for_report'] = True
                else:
                    entities = {'for_report': True}

                query_result = self.data_query_service.process_query(
                    user_message=user_message,
                    intent_info=entities
                )
            
            if not query_result['success']:
                return {
                    'success': False,
                    'message': f'无法获取数据生成报告: {query_result["message"]}'
                }
            
            data_list = query_result['data']

            # 检查数据是否为空
            if not data_list or len(data_list) == 0:
                return {
                    'success': False,
                    'message': '没有数据可用于生成报告，请调整查询条件。'
                }

            # 将字典列表转换为DataFrame以便分析
            import pandas as pd
            data_df = pd.DataFrame(data_list)

            # 智能匹配警情标签
            query_tags = None
            try:
                tag_match_result = self.police_tag_service.smart_match_tags(user_message)
                if tag_match_result and tag_match_result.get('confidence', 0) > 0.3:
                    query_tags = {
                        'level1': tag_match_result.get('matched_level1', []),
                        'level2': tag_match_result.get('matched_level2', []),
                        'level3': tag_match_result.get('matched_level3', [])
                    }
                    self.logger.info(f"智能匹配到警情标签: {query_tags}, 置信度: {tag_match_result.get('confidence')}")
            except Exception as e:
                self.logger.warning(f"警情标签匹配失败: {str(e)}")

            # 生成报告ID（使用时间戳确保唯一性）
            import time
            report_id = f'report_{int(time.time() * 1000)}'

            # 使用专业报告服务生成报告
            try:
                report_result = self.professional_report_service.generate_professional_report(
                    data_df=data_df,
                    user_requirements=user_message,
                    time_range=self._format_time_range(entities.get('time_range')),
                    query_location=entities.get('location'),
                    query_tags=query_tags,
                    report_id=report_id
                )

                if report_result['success']:
                    return {
                        'success': True,
                        'message': '专业报告生成完成！',
                        'report_content': report_result['data'],
                        'data_summary': self._format_data_for_display(data_list),
                        'total_records': len(data_df),
                        'report_id': report_id
                    }
                else:
                    return {
                        'success': False,
                        'message': f'报告生成失败：{report_result["message"]}',
                        'data_summary': self._format_data_for_display(data_list),
                        'total_records': len(data_df)
                    }

            except Exception as e:
                error_msg = str(e)
                if "超时" in error_msg or "timeout" in error_msg.lower():
                    return {
                        'success': False,
                        'message': 'LLM服务响应超时，可能是由于数据量较大。请尝试缩小查询范围或稍后重试。',
                        'data_summary': self._format_data_for_display(data_list),
                        'total_records': len(data_df),
                        'error_details': '建议：可以先查看数据摘要，或者尝试更具体的查询条件。'
                    }
                else:
                    return {
                        'success': False,
                        'message': f'报告生成失败：{error_msg}',
                        'data_summary': self._format_data_for_display(data_list),
                        'total_records': len(data_df)
                    }
            
        except Exception as e:
            self.log_error(e, "处理报告生成")
            return {
                'success': False,
                'message': f'报告生成失败: {str(e)}'
            }
    
    def _handle_data_visualization(
        self,
        entities: Dict[str, Any],
        user_message: str
    ) -> Dict[str, Any]:
        """处理数据可视化请求"""
        return {
            'success': True,
            'message': '数据可视化功能正在开发中，敬请期待！',
            'suggestion': '您可以先查询数据，然后要求生成包含图表的分析报告。'
        }
    
    def _handle_greeting(self) -> Dict[str, Any]:
        """处理问候"""
        return {
            'success': True,
            'message': '您好！我是警情数据分析助手。我可以帮您查询和分析警情数据，生成分析报告。请告诉我您需要什么帮助？'
        }
    
    def _handle_farewell(self) -> Dict[str, Any]:
        """处理告别"""
        return {
            'success': True,
            'message': '再见！感谢使用警情数据分析系统。如有需要，随时可以回来咨询。'
        }
    
    def _handle_general_question(
        self,
        user_message: str,
        entities: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理一般问题"""
        return {
            'success': True,
            'message': '我是专门的警情数据分析助手。我可以帮您：\n\n'
                      '1. 查询特定时间、地区的警情数据\n'
                      '2. 生成警情分析报告\n'
                      '3. 统计警情分布情况\n\n'
                      '请告诉我您具体需要什么帮助？',
            'suggestions': [
                '查询最近一个月的警情数据',
                '生成本季度的警情分析报告',
                '统计各地区的警情分布'
            ]
        }
    
    def _generate_data_summary(self, data_df: pd.DataFrame) -> str:
        """生成数据摘要"""
        try:
            summary_parts = []
            
            # 基本统计
            total_count = len(data_df)
            summary_parts.append(f"数据概况：共 {total_count} 条记录")
            
            # 时间范围
            date_columns = [col for col in data_df.columns if '时间' in col or 'time' in col.lower() or 'bjsj' in col.lower()]
            if date_columns:
                date_col = date_columns[0]
                if not data_df[date_col].empty:
                    try:
                        date_series = pd.to_datetime(data_df[date_col], errors='coerce').dropna()
                        if not date_series.empty:
                            min_date = date_series.min().strftime('%Y-%m-%d')
                            max_date = date_series.max().strftime('%Y-%m-%d')
                            summary_parts.append(f"时间范围：{min_date} 至 {max_date}")
                    except:
                        pass
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            self.log_error(e, "生成数据摘要")
            return f"数据概况：共 {len(data_df)} 条记录"
    
    def _format_data_for_display(self, data, max_rows: int = 10) -> Dict[str, Any]:
        """格式化数据用于显示"""
        try:
            # 如果是字典列表，直接处理
            if isinstance(data, list):
                if not data:
                    return {
                        'columns': [],
                        'data': [],
                        'total_rows': 0,
                        'displayed_rows': 0
                    }

                # 限制显示行数
                display_data = data[:max_rows]

                # 获取列名（从第一条记录）
                columns = list(data[0].keys()) if data else []

                return {
                    'columns': columns,
                    'data': display_data,
                    'total_rows': len(data),
                    'displayed_rows': len(display_data)
                }

            # 如果是DataFrame，转换为字典列表
            elif hasattr(data, 'head') and hasattr(data, 'to_dict'):
                # 限制显示行数
                display_df = data.head(max_rows)

                return {
                    'columns': list(display_df.columns),
                    'data': display_df.to_dict('records'),
                    'total_rows': len(data),
                    'displayed_rows': len(display_df)
                }

            else:
                # 其他类型，尝试转换
                return {
                    'columns': [],
                    'data': [{'value': str(data)}],
                    'total_rows': 1,
                    'displayed_rows': 1
                }

        except Exception as e:
            self.log_error(e, "格式化显示数据")
            return {
                'columns': [],
                'data': [],
                'total_rows': 0,
                'displayed_rows': 0,
                'error': str(e)
            }
    
    def _generate_comprehensive_analysis(self, data_df: pd.DataFrame) -> str:
        """生成综合分析"""
        # 这里可以集成原来的DataAnalyzer功能
        # 暂时返回基础分析
        return f"数据分析结果：\n共有 {len(data_df)} 条记录\n包含 {len(data_df.columns)} 个字段"
    
    def _format_time_range(self, time_range: Dict[str, Any]) -> str:
        """格式化时间范围"""
        if not time_range:
            return "未指定时间范围"
        
        if isinstance(time_range, dict):
            start = time_range.get('start_date', '')
            end = time_range.get('end_date', '')
            desc = time_range.get('description', '')
            
            if desc:
                return desc
            elif start and end:
                return f"{start} 至 {end}"
            elif start:
                return f"从 {start} 开始"
            elif end:
                return f"到 {end} 结束"
        
        return str(time_range)

    def _generate_report_from_cached_data(
        self,
        cached_query_data: Dict[str, Any],
        user_message: str,
        session_id: str
    ) -> Dict[str, Any]:
        """基于缓存的查询数据生成报告"""
        try:
            data_list = cached_query_data.get('data', [])
            query_info = cached_query_data.get('query_info', {})

            if not data_list or len(data_list) == 0:
                return {
                    'success': False,
                    'message': '缓存的查询数据为空，无法生成报告。请重新查询数据。'
                }

            # 将字典列表转换为DataFrame
            import pandas as pd
            data_df = pd.DataFrame(data_list)

            # 构建查询标签信息
            query_tags = {}
            if 'query_params' in query_info:
                params = query_info['query_params']
                if params.get('situation_types'):
                    query_tags['situation_types'] = params['situation_types']
                if params.get('situation_categories'):
                    query_tags['situation_categories'] = params['situation_categories']

            # 生成报告ID（使用时间戳确保唯一性）
            import time
            report_id = f'report_{int(time.time() * 1000)}'

            # 使用专业报告服务生成报告
            report_result = self.professional_report_service.generate_professional_report(
                data_df=data_df,
                user_requirements=user_message,
                time_range=self._extract_time_range_from_cache(cached_query_data),
                query_location=self._extract_location_from_cache(cached_query_data),
                query_tags=query_tags,
                report_id=report_id
            )

            if report_result['success']:
                return {
                    'success': True,
                    'message': '基于已查询数据生成报告完成！',
                    'report_content': report_result['data'],
                    'data_summary': self._format_data_for_display(data_list),
                    'total_records': len(data_df),
                    'cached_data_used': True,
                    'report_id': report_id
                }
            else:
                return {
                    'success': False,
                    'message': f'报告生成失败：{report_result["message"]}',
                    'data_summary': self._format_data_for_display(data_list),
                    'total_records': len(data_df)
                }

        except Exception as e:
            self.log_error(e, "基于缓存数据生成报告")
            return {
                'success': False,
                'message': f'基于缓存数据生成报告失败: {str(e)}'
            }

    def _extract_time_range_from_cache(self, cached_data: Dict[str, Any]) -> str:
        """从缓存数据中提取时间范围"""
        try:
            query_info = cached_data.get('query_info', {})
            data_range = query_info.get('data_range', {})
            start_date = data_range.get('start_date')
            end_date = data_range.get('end_date')

            if start_date and end_date:
                return f"{start_date} 至 {end_date}"
            elif start_date:
                return f"从 {start_date} 开始"
            elif end_date:
                return f"到 {end_date} 结束"
            else:
                return "数据集完整时间范围"
        except:
            return "数据集完整时间范围"

    def _extract_location_from_cache(self, cached_data: Dict[str, Any]) -> Dict[str, Any]:
        """从缓存数据中提取地点信息"""
        try:
            query_info = cached_data.get('query_info', {})
            query_params = query_info.get('query_params', {})

            location = {}
            if query_params.get('counties'):
                location['county'] = query_params['counties']
            if query_params.get('towns'):
                location['town'] = query_params['towns']
            if query_params.get('communities'):
                location['community'] = query_params['communities']

            return location
        except:
            return {}
    

