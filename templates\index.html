<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警情分析智能体</title>
    
    <!-- 引入Element Plus CSS -->
    <link rel="stylesheet" href="/static/lib/element-plus/index.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="/static/lib/fontawesome/css/all.min.css">
    <!-- 引入共享样式 -->
    <link rel="stylesheet" href="/static/css/common.css">
    <!-- 添加favicon -->
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    
    <style>
        /* 页面特定样式 */
        body {
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 防止页面滚动 */
        }

        .app-container {
            max-width: 1400px;
            width: 90%;
            margin: 0 auto;
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            box-sizing: border-box;
            animation: fadeIn 0.6s ease-out;
            overflow: hidden; /* 防止容器滚动 */
        }


        
        /* 页面头部 */
        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px 24px;
            background: var(--bg-gradient);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.2), 0 4px 16px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .app-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: white;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 16px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .app-title i {
            font-size: 2rem;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            z-index: 1;
        }

        .header-controls .el-button {
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            background: rgba(255, 255, 255, 0.1) !important;
            color: white !important;
            font-weight: 600;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .header-controls .el-button:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        /* 卡片样式 */
        .el-card {
            width: 100%;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(14, 165, 233, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(14, 165, 233, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .el-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            width: 100%;
            min-height: 0;
            overflow: hidden; /* 防止主内容区域滚动 */
        }

        /* 聊天容器 */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 200px);
            min-height: 600px;
            overflow: hidden; /* 防止聊天容器滚动 */
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            width: 100%;
            min-height: 0;
            overflow: hidden; /* 防止聊天主区域滚动 */
        }

        .chat-sidebar {
            width: 320px;
            display: none;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto; /* 只有这里允许滚动 */
            overflow-x: hidden; /* 防止水平滚动 */
            padding: 24px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(14, 165, 233, 0.2);
            margin-bottom: 20px;
            position: relative;
            min-height: 0; /* 确保flex子项可以收缩 */
        }

        .chat-messages::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }
        
        /* 消息样式 */
        .message {
            margin-bottom: 16px;
            display: flex;
            flex-direction: column;
        }
        
        .message-user {
            align-items: flex-end;
        }
        
        .message-system {
            align-items: flex-start;
        }
        
        .message-content {
            padding: 16px 20px;
            border-radius: 18px;
            max-width: 85%;
            word-break: break-word;
            position: relative;
            box-shadow: var(--card-shadow);
            transition: all 0.2s ease;
            z-index: 1;
        }

        .message-content:hover {
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .message-user .message-content {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-system .message-content {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            color: var(--text-color);
            border: 1px solid rgba(14, 165, 233, 0.2);
            border-bottom-left-radius: 4px;
        }
        
        /* Markdown 内容样式 */
        .message-system .message-content a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .message-system .message-content a:hover {
            text-decoration: underline;
        }
        
        .message-system .message-content p {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
        }
        
        .message-system .message-content p:first-child {
            margin-top: 0;
        }
        
        .message-system .message-content p:last-child {
            margin-bottom: 0;
        }
        

        

        
        /* 表格和代码样式 */
        .message-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
            font-size: 0.9rem;
        }
        
        .message-content table th,
        .message-content table td {
            padding: 8px;
            text-align: left;
            border: 1px solid #EBEEF5;
        }
        
        .message-content table th {
            background-color: #f5f7fa;
        }
        
        .message-content pre {
            background-color: #f5f7fa;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.85rem;
        }
        
        .message-content code {
            font-family: 'Source Code Pro', Consolas, Monaco, 'Andale Mono', monospace;
            color: #476582;
        }
        
        /* 美化滚动条 - 只在聊天消息区域显示 */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(14, 165, 233, 0.1);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(14, 165, 233, 0.3);
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(14, 165, 233, 0.5);
        }

        /* 确保其他区域不显示滚动条 */
        body::-webkit-scrollbar,
        .app-container::-webkit-scrollbar,
        .main-content::-webkit-scrollbar,
        .chat-container::-webkit-scrollbar,
        .chat-main::-webkit-scrollbar {
            display: none;
        }
        
        /* 查询建议样式 */
        .query-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
            align-items: center;
        }
        
        .suggestion-tag {
            background-color: #f2f6fc;
            color: var(--primary-color);
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            border: 1px solid #e4e7ed;
            animation: fadeIn 0.5s ease;
            animation-fill-mode: both;
        }
        
        .suggestion-tag:nth-child(2) { animation-delay: 0.1s; }
        .suggestion-tag:nth-child(3) { animation-delay: 0.2s; }
        .suggestion-tag:nth-child(4) { animation-delay: 0.3s; }
        .suggestion-tag:nth-child(5) { animation-delay: 0.4s; }
        .suggestion-tag:nth-child(6) { animation-delay: 0.5s; }
        
        .suggestion-tag:hover {
            background-color: #ecf5ff;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* 输入区域样式 */
        .input-area {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(14, 165, 233, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            flex-shrink: 0; /* 防止输入区域被压缩 */
        }

        .input-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        .input-area:focus-within {
            box-shadow: 0 12px 40px rgba(14, 165, 233, 0.15), 0 6px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .input-area .el-textarea__inner {
            border: none !important;
            background: transparent !important;
            box-shadow: none !important;
            resize: none !important;
            font-size: 16px !important;
            line-height: 1.6 !important;
            padding: 12px 0 !important;
            position: relative;
            z-index: 1;
        }

        .input-area .el-textarea__inner:focus {
            border: none !important;
            box-shadow: none !important;
        }

        /* 输入区域按钮样式统一 */
        .input-area .el-button {
            padding: 12px 20px;
            font-weight: 600;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            border: 2px solid rgba(14, 165, 233, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 44px;
        }

        .input-area .el-button--primary {
            background: var(--bg-gradient);
            border: 2px solid transparent;
            color: white;
            box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4);
            font-size: 15px;
            font-weight: 700;
        }

        .input-area .el-button--primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.5);
            background: linear-gradient(135deg, #1e40af 0%, #0ea5e9 100%);
        }

        .input-area .el-button--info {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 249, 255, 0.95) 100%);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            backdrop-filter: blur(10px);
            font-size: 15px;
            font-weight: 700;
            box-shadow: 0 4px 16px rgba(14, 165, 233, 0.2);
        }

        .input-area .el-button--info:hover {
            background: linear-gradient(135deg, rgba(224, 242, 254, 0.95) 0%, rgba(186, 230, 253, 0.95) 100%);
            border-color: #1e40af;
            color: #1e40af;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(14, 165, 233, 0.3);
        }

        /* 按钮图标样式 */
        .input-area .el-button i {
            margin-right: 8px;
            font-size: 14px;
        }

        /* 标签选择器弹窗样式 */
        .tag-selector-dialog {
            border-radius: 16px;
            overflow: hidden;
        }

        .tag-selector-dialog .el-dialog {
            border-radius: 16px;
            overflow: hidden;
            max-height: 80vh;
        }

        .tag-selector-dialog .el-dialog__header {
            background: var(--bg-gradient);
            color: white;
            padding: 20px 32px;
            margin: 0;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tag-selector-dialog .el-dialog__header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .tag-selector-dialog .el-dialog__title {
            color: white;
            font-weight: 700;
            font-size: 18px;
            position: relative;
            z-index: 1;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            margin: 0;
        }

        .tag-selector-dialog .el-dialog__headerbtn {
            position: absolute;
            top: 16px;
            right: 20px;
            z-index: 2;
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .tag-selector-dialog .el-dialog__headerbtn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .tag-selector-dialog .el-dialog__headerbtn .el-dialog__close {
            color: white;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .tag-selector-dialog .el-dialog__headerbtn .el-dialog__close:hover {
            color: white;
        }

        .tag-selector-content {
            padding: 24px 32px;
            background: linear-gradient(135deg, var(--bg-color) 0%, #e0f2fe 50%, var(--bg-light) 100%);
            max-height: 60vh;
            overflow-y: auto;
        }

        .selector-section {
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 6px 24px rgba(14, 165, 233, 0.1), 0 3px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(14, 165, 233, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .selector-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        .selector-section:hover {
            box-shadow: 0 12px 40px rgba(14, 165, 233, 0.15), 0 6px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 3px solid var(--primary-color);
            position: relative;
            z-index: 1;
        }

        .section-header i {
            color: var(--primary-color);
            font-size: 20px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(14, 165, 233, 0.1);
            border-radius: 50%;
            padding: 8px;
        }

        .section-header h4 {
            margin: 0;
            color: var(--primary-dark);
            font-size: 18px;
            font-weight: 800;
        }

        .time-picker {
            border-radius: 8px;
        }

        .time-picker .el-input__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .time-picker .el-input__wrapper:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .tag-level-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .tag-level-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: linear-gradient(135deg, rgba(240, 249, 255, 0.8) 0%, rgba(224, 242, 254, 0.8) 100%);
            border-radius: 12px;
            border: 1px solid rgba(14, 165, 233, 0.2);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .tag-level-item:hover {
            background: linear-gradient(135deg, rgba(224, 242, 254, 0.9) 0%, rgba(186, 230, 253, 0.9) 100%);
            border-color: var(--primary-color);
            box-shadow: 0 4px 16px rgba(14, 165, 233, 0.2);
            transform: translateY(-1px);
        }

        .tag-level-label {
            min-width: 90px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 15px;
            position: relative;
        }

        .tag-level-label::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 2px;
            height: 16px;
            background: var(--primary-color);
            border-radius: 1px;
        }

        .tag-select {
            flex: 1;
            max-width: 450px;
        }

        .tag-select .el-select__wrapper {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .tag-select .el-select__wrapper:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }



        /* 弹窗底部按钮样式 */
        .tag-selector-dialog .el-dialog__footer {
            padding: 16px 32px;
            background: linear-gradient(135deg, var(--bg-color) 0%, #e0f2fe 100%);
            border-top: 1px solid rgba(14, 165, 233, 0.2);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .tag-selector-dialog .el-dialog__footer .el-button {
            padding: 10px 20px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .tag-selector-dialog .el-dialog__footer .el-button--primary {
            background: var(--bg-gradient);
            border: none;
            box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3);
        }

        .tag-selector-dialog .el-dialog__footer .el-button--primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4);
        }
        
        /* 加载动画 */
        .loading-dots {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: inline-block;
            animation: pulse 1.5s infinite ease-in-out;
        }

        .loading-dots span:nth-child(2) {
            animation-delay: 0.3s;
        }

        .loading-dots span:nth-child(3) {
            animation-delay: 0.6s;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(0.75);
                opacity: 0.5;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 进度条样式 */
        .progress-container {
            padding: 16px;
            background: linear-gradient(135deg, rgba(240, 249, 255, 0.95) 0%, rgba(224, 242, 254, 0.95) 100%);
            border-radius: 12px;
            border: 1px solid rgba(14, 165, 233, 0.3);
            margin: 0;
            backdrop-filter: blur(10px);
        }

        .progress-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 15px;
        }

        .progress-header i {
            font-size: 16px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Element Plus 进度条样式覆盖 */
        .progress-container .el-progress {
            margin-bottom: 16px;
        }

        .progress-container .el-progress__text {
            color: var(--primary-color) !important;
            font-weight: 600 !important;
            font-size: 14px !important;
        }

        .progress-steps {
            margin-top: 12px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 6px 10px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 13px;
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(14, 165, 233, 0.1);
        }

        .progress-step.completed {
            background: rgba(34, 197, 94, 0.15);
            color: #059669;
            border-color: rgba(34, 197, 94, 0.3);
        }

        .progress-step.current {
            background: rgba(14, 165, 233, 0.15);
            color: var(--primary-color);
            font-weight: 600;
            border-color: rgba(14, 165, 233, 0.4);
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
        }

        .progress-step i {
            width: 14px;
            text-align: center;
            font-size: 12px;
        }

        .progress-step.completed i {
            color: #059669;
        }

        .progress-step.current i {
            color: var(--primary-color);
            animation: pulse 1.5s infinite ease-in-out;
        }

        /* 报告按钮样式 */
        .report-buttons {
            display: flex;
            gap: 12px;
            margin-top: 16px;
            flex-wrap: wrap;
        }

        .report-button {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 14px;
        }

        .report-button.view-report {
            background: var(--bg-gradient);
            color: white !important;
            box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3);
        }

        .report-button.view-report:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4);
            text-decoration: none !important;
            color: white !important;
        }

        .report-button.view-report i {
            color: white !important;
        }

        .report-button.download-report {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.9) 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(34, 197, 94, 0.3);
        }

        .report-button.download-report:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
            text-decoration: none;
            color: white;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .chat-container {
                flex-direction: column;
                height: auto;
            }
            
            .chat-sidebar {
                width: 100%;
            }
        }

        /* 查询结果样式 - 直接在message-content中显示 */

        .query-result-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f1f5f9;
        }

        .query-result-header i {
            color: var(--primary-color);
            font-size: 24px;
            background: rgba(59, 130, 246, 0.1);
            padding: 12px;
            border-radius: 12px;
        }

        .query-result-header h4 {
            margin: 0;
            color: var(--text-color);
            font-size: 20px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .total-count {
            background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 600;
            margin-left: auto;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5); }
        }

        .query-result-content h5 {
            margin: 0 0 16px 0;
            color: var(--text-color);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .query-result-content h5::before {
            content: '📊';
            font-size: 18px;
        }

        .data-records {
            background: white;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .data-record-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
            flex-wrap: wrap;
            transition: all 0.2s ease;
        }

        .data-record-item:hover {
            background: #f8fafc;
            margin: 0 -8px;
            padding: 12px 8px;
            border-radius: 8px;
        }

        .data-record-item:last-child {
            border-bottom: none;
        }

        .record-index {
            color: var(--primary-color);
            font-weight: 700;
            min-width: 24px;
            background: rgba(59, 130, 246, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }

        .record-field {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 13px;
            color: var(--text-color);
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .record-field:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            transform: translateY(-1px);
        }

        .query-result-actions {
            display: flex;
            gap: 16px;
            margin-top: 24px;
            flex-wrap: wrap;
            padding-top: 20px;
            border-top: 2px solid #f1f5f9;
        }

        .query-result-actions .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            position: relative;
            overflow: hidden;
            min-width: 120px;
            text-align: center;
        }

        .query-result-actions .btn span {
            font-size: 13px;
            line-height: 1.2;
        }

        .query-result-actions .btn small {
            font-size: 11px;
            opacity: 0.8;
            font-weight: 400;
        }

        .query-result-actions .btn i {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .query-result-actions .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .query-result-actions .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            color: #166534;
            border: 2px solid #bbf7d0;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(34, 197, 94, 0.3);
        }

        /* 全量数据弹窗样式 */
        .full-data-dialog .el-dialog__body {
            padding: 20px;
        }

        .data-table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .table-actions {
            display: flex;
            gap: 8px;
        }

        .table-pagination {
            margin-top: 16px;
            display: flex;
            justify-content: center;
        }

        /* 报告链接卡片样式 */
        .report-link-card {
            background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
            border: 2px solid #bfdbfe;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .report-link-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
        }

        .report-link-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid #e0f2fe;
        }

        .report-link-header i {
            color: #3b82f6;
            font-size: 20px;
            background: rgba(59, 130, 246, 0.1);
            padding: 10px;
            border-radius: 10px;
        }

        .report-link-header h4 {
            margin: 0;
            color: #1e40af;
            font-size: 18px;
            font-weight: 600;
        }

        .report-link-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .report-link-actions .btn {
            text-decoration: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        /* 加载动画样式 */
        .loading-spinner {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            color: var(--primary-color);
            font-size: 14px;
        }

        .loading-spinner i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }



    </style>
</head>
<body>
    <!-- 主容器 -->
    <div id="app" class="app-container">
        <!-- 页面头部 -->
        <header class="app-header">
            <h1 class="app-title">
                <i class="fas fa-shield-alt"></i>
                警情分析智能体
            </h1>
            <div class="header-controls">
                <el-button @click="showUsageGuide" size="small">
                    <i class="fas fa-lightbulb"></i> 使用指南
                </el-button>
                <el-button @click="clearChat" size="small">
                    <i class="fas fa-eraser"></i> 清空会话
                </el-button>
            </div>
        </header>

        <!-- 主内容 -->
        <div class="main-content">
            <!-- 聊天界面 -->
            <div class="chat-container">
            <div class="chat-main">
                {# 在这里添加最近报告的链接 #}
                {% if most_recent_report and most_recent_report.url %}
                    <div class="recent-report-container" style="padding: 10px; margin-bottom: 10px; border: 1px solid #ddd; background-color: #f9f9f9; text-align: center; border-radius: 4px;">
                        <p style="margin: 0; font-size: 14px;">
                            <strong>快速访问：</strong>
                            <a href="{{ most_recent_report.url }}" target="_blank" style="color: #007bff; text-decoration: none; font-weight: 500;">
                                查看最近生成的报告 (ID: {{ most_recent_report.id }})
                            </a>
                        </p>
                    </div>
                {% endif %}

                <div class="chat-messages" ref="messagesContainer">
                    <!-- 系统欢迎消息 -->
                    <div class="message message-system">
                        <div class="message-content">
                            <p><strong>👋 您好，我是警情数据分析智能体</strong></p>
                            <p>我可以帮您：</p>
                            <ul>
                                <li>查询和分析警情数据</li>
                                <li>生成分析报告和可视化图表</li>
                                <li>分析警情趋势和分布规律</li>
                                <li>回答警情数据相关问题</li>
                            </ul>
                            <p>请在下方输入您的分析需求，或点击快速提问标签开始。</p>
                        </div>
                    </div>
                    
                    <!-- 用户消息 -->
                    <div v-for="(message, index) in messages" :key="`msg-${index}-${message.role}`" class="message" :class="message.role === 'user' ? 'message-user' : 'message-system'">
                        <div class="message-content" v-if="message.role === 'user'">[[ message.content ]]</div>
                        <div class="message-content" v-else v-html="getProcessedMessage(message.content, index)"></div>
                    </div>
                    
                    <!-- 进度显示消息 -->
                    <div v-if="showProgress" class="message message-system">
                        <div class="message-content">
                            <div class="progress-container">
                                <div class="progress-header">
                                    <i class="fas fa-cogs"></i>
                                    <span>[[ progressText ]]</span>
                                </div>
                                <el-progress
                                    :percentage="progressPercentage"
                                    :status="progressStatus"
                                    :stroke-width="8"
                                    :show-text="true">
                                </el-progress>
                                <div class="progress-steps" v-if="progressSteps.length > 0">
                                    <div
                                        v-for="(step, index) in progressSteps"
                                        :key="index"
                                        class="progress-step"
                                        :class="{ 'completed': step.completed, 'current': step.current }">
                                        <i :class="step.icon"></i>
                                        <span>[[ step.text ]]</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载指示器 -->
                    <div v-if="isLoading && !showProgress" class="message message-system">
                        <div class="message-content">
                            <div class="loading-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 输入框部分 -->
                <div class="input-area">
                    <el-input
                        v-model="inputMessage"
                        type="textarea"
                        :rows="2"
                        :disabled="isLoading"
                        @keydown.enter.native.prevent="sendMessage"
                    ></el-input>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px; gap: 12px;">
                        <el-button @click="showTagSelector" size="default" type="info" style="flex: 1; max-width: 140px; height: 44px; font-size: 15px; font-weight: 600;">
                            <i class="fas fa-tags" style="margin-right: 8px;"></i>智能标签
                        </el-button>
                        <el-button type="primary" @click="sendMessage" :loading="isLoading" style="flex: 1; max-width: 120px; height: 44px; font-size: 15px; font-weight: 600;">
                            <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>发送
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 右侧边栏 - 移除原有的使用提示卡片 -->
            <div class="chat-sidebar" style="display: none;">
                <!-- 侧边栏内容已移除 -->
            </div>
            </div>
        </div>

        <!-- 使用指南弹窗 -->
        <el-dialog v-model="usageGuideVisible" title="使用指南" width="600px" :close-on-click-modal="false">
            <div>
                <h4><i class="fas fa-lightbulb"></i> 使用提示</h4>
                <ul style="padding-left: 20px; margin: 0 0 16px 0;">
                    <li style="margin-bottom: 8px;"><i class="fas fa-check-circle" style="color: #67C23A;"></i> 指定时间范围（如2025年1月-4月）可获得更精确的分析</li>
                    <li style="margin-bottom: 8px;"><i class="fas fa-check-circle" style="color: #67C23A;"></i> 可以生成报告并查看可视化图表</li>
                    <li style="margin-bottom: 8px;"><i class="fas fa-check-circle" style="color: #67C23A;"></i> 支持按全市、区县、街道、社区分析</li>
                    <li style="margin-bottom: 8px;"><i class="fas fa-check-circle" style="color: #67C23A;"></i> 系统支持多轮对话，可以继续提问</li>
                </ul>

                <div style="background-color: #ecf5ff; padding: 12px; border-radius: 4px; margin-top: 16px;">
                    <h4 style="margin-top: 0;"><i class="fas fa-info-circle"></i> 功能指南</h4>
                    <ul style="padding-left: 20px; margin: 0; font-size: 13px;">
                        <li style="margin-bottom: 6px;">数据查询：可按时间、地区、类型筛选</li>
                        <li style="margin-bottom: 6px;">数据分析：趋势分析、分布分析、关联分析</li>
                        <li style="margin-bottom: 6px;">报告生成：生成完整分析报告</li>
                        <li style="margin-bottom: 6px;">问答交互：针对数据的自然语言问答</li>
                    </ul>
                    <p style="font-size: 12px; margin-top: 8px; margin-bottom: 0; color: #666;">
                        提示: 您可以使用智能标签功能快速选择查询条件。
                    </p>
                </div>
            </div>
            <template #footer>
                <el-button @click="usageGuideVisible = false">关闭</el-button>
            </template>
        </el-dialog>

        <!-- 智能标签选择弹窗 -->
        <el-dialog v-model="tagSelectorVisible" title="智能标签选择" width="700px" :close-on-click-modal="false" class="tag-selector-dialog" :show-close="true">
            <div v-loading="tagLoading" class="tag-selector-content">
                <!-- 时间范围选择 -->
                <div class="selector-section">
                    <div class="section-header">
                        <i class="fas fa-calendar-alt"></i>
                        <h4>时间范围</h4>
                    </div>
                    <el-date-picker
                        v-model="selectedTimeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        format="YYYY年MM月DD日 HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledDate"
                        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                        style="width: 100%;"
                        class="time-picker">
                    </el-date-picker>
                </div>

                <!-- 警情类型选择 -->
                <div class="selector-section">
                    <div class="section-header">
                        <i class="fas fa-tags"></i>
                        <h4>警情类型</h4>
                    </div>
                    <div class="tag-level-container">
                        <div class="tag-level-item">
                            <label class="tag-level-label">一级分类：</label>
                            <el-select
                                v-model="selectedLevel1"
                                placeholder="请选择一级分类"
                                @change="onLevel1Change"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in level1Options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="tag-level-item" v-if="level2Options.length > 0">
                            <label class="tag-level-label">二级分类：</label>
                            <el-select
                                v-model="selectedLevel2"
                                placeholder="请选择二级分类"
                                @change="onLevel2Change"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in level2Options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="tag-level-item" v-if="level3Options.length > 0">
                            <label class="tag-level-label">三级分类：</label>
                            <el-select
                                v-model="selectedLevel3"
                                placeholder="请选择三级分类"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in level3Options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>

                <!-- 地址标签选择 -->
                <div class="selector-section">
                    <div class="section-header">
                        <i class="fas fa-map-marker-alt"></i>
                        <h4>地址标签</h4>
                    </div>
                    <div class="tag-level-container">
                        <div class="tag-level-item">
                            <label class="tag-level-label">县（市、区）：</label>
                            <el-select
                                v-model="selectedCounty"
                                placeholder="请选择县区"
                                @change="onCountyChange"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in countyOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="tag-level-item" v-if="townOptions.length > 0">
                            <label class="tag-level-label">街道（镇）：</label>
                            <el-select
                                v-model="selectedTown"
                                placeholder="请选择街道"
                                @change="onTownChange"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in townOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="tag-level-item" v-if="policeStationOptions.length > 0 || communityOptions.length > 0">
                            <div style="display: flex; gap: 20px;">
                                <div style="flex: 1;" v-if="policeStationOptions.length > 0">
                                    <label class="tag-level-label">警务室：</label>
                                    <el-select
                                        v-model="selectedPoliceStation"
                                        placeholder="请选择警务室（可选）"
                                        @change="onPoliceStationChange"
                                        multiple
                                        collapse-tags
                                        collapse-tags-tooltip
                                        class="tag-select"
                                        style="width: 100%;">
                                        <el-option
                                            v-for="item in policeStationOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </div>
                                <div style="flex: 1;" v-if="communityOptions.length > 0">
                                    <label class="tag-level-label">社区：</label>
                                    <el-select
                                        v-model="selectedCommunity"
                                        placeholder="请选择社区"
                                        multiple
                                        collapse-tags
                                        collapse-tags-tooltip
                                        class="tag-select"
                                        style="width: 100%;">
                                        <el-option
                                            v-for="item in filteredCommunityOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发生处所选择 -->
                <div class="selector-section">
                    <div class="section-header">
                        <i class="fas fa-location-arrow"></i>
                        <h4>发生处所</h4>
                    </div>
                    <div class="tag-level-container">
                        <div class="tag-level-item">
                            <el-select
                                v-model="selectedPlaceOccur"
                                placeholder="请选择发生处所"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select"
                                style="width: 100%;">
                                <el-option
                                    v-for="item in placeOccurOptions"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>

                <!-- 场所类型选择 -->
                <div class="selector-section">
                    <div class="section-header">
                        <i class="fas fa-building"></i>
                        <h4>场所类型</h4>
                    </div>
                    <div class="tag-level-container">
                        <div class="tag-level-item">
                            <label class="tag-level-label">场所类别：</label>
                            <el-select
                                v-model="selectedPlaceCategory"
                                placeholder="请选择场所类别"
                                @change="onPlaceCategoryChange"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in placeCategoryOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="tag-level-item" v-if="placeMainCategoryOptions.length > 0">
                            <label class="tag-level-label">场所大类：</label>
                            <el-select
                                v-model="selectedPlaceMainCategory"
                                placeholder="请选择场所大类"
                                multiple
                                collapse-tags
                                collapse-tags-tooltip
                                class="tag-select">
                                <el-option
                                    v-for="item in placeMainCategoryOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </div>


            </div>
            <template #footer>
                <el-button @click="tagSelectorVisible = false">取消</el-button>
                <el-button type="primary" @click="applyTagSelection">应用选择</el-button>
            </template>
        </el-dialog>

        <!-- 全量数据表格弹窗 -->
        <el-dialog v-model="fullDataTableVisible" title="全量数据查看" width="95%" :close-on-click-modal="false" class="full-data-dialog">
            <div class="full-data-content">
                <div class="data-table-header">
                    <span>共 [[ currentFullData.length ]] 条记录</span>
                    <div class="table-actions">
                        <!-- CSV导出功能已移除，统一使用Excel导出 -->
                    </div>
                </div>

                <el-table
                    :data="currentFullData.slice((tableCurrentPage - 1) * tablePageSize, tableCurrentPage * tablePageSize)"
                    style="width: 100%"
                    height="500"
                    stripe
                    border>
                    <el-table-column prop="jjdbh" label="接警单编号" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="bjsj" label="报警时间" width="150" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="place_remark" label="警情详址" width="200" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="county_feedback" label="县（市区）" width="100" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="town" label="街道（镇）" width="100" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="police_station" label="警务室" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="community" label="社区" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="place_category" label="场所类别" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="place_main_category" label="场所大类" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="place_occur" label="发生处所" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="police_situation_category" label="警情类别" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="police_situation_type" label="警情类型" width="150" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="cause" label="发生原因" width="150" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="cjqk" label="出警情况" width="150" show-overflow-tooltip></el-table-column>
                </el-table>

                <div class="table-pagination">
                    <el-pagination
                        v-model:current-page="tableCurrentPage"
                        v-model:page-size="tablePageSize"
                        :page-sizes="[20, 50, 100, 200]"
                        :total="currentFullData.length"
                        layout="total, sizes, prev, pager, next, jumper"
                        background>
                    </el-pagination>
                </div>
            </div>
            <template #footer>
                <el-button @click="closeFullDataTable">关闭</el-button>
            </template>
        </el-dialog>
    </div>

    <!-- JavaScript 引用 -->
    <script src="/static/lib/vue/vue.global.prod.js"></script>
    <script src="/static/lib/element-plus/index.js"></script>
    <script src="/static/lib/markdown-it/markdown-it.min.js"></script>
    <script>
        // Element Plus 中文语言包
        const ElementPlusLocaleZhCn = {
            name: 'zh-cn',
            el: {
                datepicker: {
                    now: '此刻',
                    today: '今天',
                    cancel: '取消',
                    clear: '清空',
                    confirm: '确定',
                    selectDate: '选择日期',
                    selectTime: '选择时间',
                    startDate: '开始日期',
                    startTime: '开始时间',
                    endDate: '结束日期',
                    endTime: '结束时间',
                    prevYear: '前一年',
                    nextYear: '后一年',
                    prevMonth: '上个月',
                    nextMonth: '下个月',
                    year: '年',
                    month1: '1月',
                    month2: '2月',
                    month3: '3月',
                    month4: '4月',
                    month5: '5月',
                    month6: '6月',
                    month7: '7月',
                    month8: '8月',
                    month9: '9月',
                    month10: '10月',
                    month11: '11月',
                    month12: '12月',
                    weeks: {
                        sun: '日',
                        mon: '一',
                        tue: '二',
                        wed: '三',
                        thu: '四',
                        fri: '五',
                        sat: '六'
                    },
                    months: {
                        jan: '一月',
                        feb: '二月',
                        mar: '三月',
                        apr: '四月',
                        may: '五月',
                        jun: '六月',
                        jul: '七月',
                        aug: '八月',
                        sep: '九月',
                        oct: '十月',
                        nov: '十一月',
                        dec: '十二月'
                    }
                }
            }
        }
    </script>
    <script>
        const { createApp, ref, computed, onMounted, nextTick, getCurrentInstance, onBeforeUnmount } = Vue
        
        const app = createApp({
            delimiters: ['[[', ']]'],  // 使用自定义分隔符，避免与Jinja2冲突
            setup() {
                const messages = ref([])
                const inputMessage = ref('')
                const isLoading = ref(false)
                const messagesContainer = ref(null)
                // const isDark = ref(false) // 移除 isDark

                // 进度条相关
                const showProgress = ref(false)
                const progressText = ref('正在处理...')
                const progressPercentage = ref(0)
                const progressStatus = ref('')
                const progressSteps = ref([])

                // 弹窗控制
                const usageGuideVisible = ref(false)
                const tagSelectorVisible = ref(false)
                const tagLoading = ref(false)

                // 标签选择相关数据
                const selectedTimeRange = ref([])
                const selectedLevel1 = ref([])
                const selectedLevel2 = ref([])
                const selectedLevel3 = ref([])
                const level1Options = ref([])
                const level2Options = ref([])
                const level3Options = ref([])
                const tagHierarchyData = ref({})

                // 地址标签相关数据
                const selectedCounty = ref([])
                const selectedTown = ref([])
                const selectedPoliceStation = ref([])
                const selectedCommunity = ref([])
                const countyOptions = ref([])
                const townOptions = ref([])
                const policeStationOptions = ref([])
                const communityOptions = ref([])
                const placeHierarchyData = ref({})

                // 发生处所相关数据
                const selectedPlaceOccur = ref([])
                const placeOccurOptions = ref([])

                // 场所类型相关数据
                const selectedPlaceMainCategory = ref([])
                const selectedPlaceCategory = ref([])
                const placeMainCategoryOptions = ref([])
                const placeCategoryOptions = ref([])
                const placeCategoryHierarchyData = ref({})

                // 数据预览相关
                const dataPreviewVisible = ref(false)
                const currentDataPreview = ref(null)
                const fullDataTableVisible = ref(false)
                const currentFullData = ref([])
                const tableCurrentPage = ref(1)
                const tablePageSize = ref(20)

                // 计算属性：根据选中的警务室过滤社区选项
                const filteredCommunityOptions = computed(() => {
                    if (!selectedPoliceStation.value || selectedPoliceStation.value.length === 0) {
                        // 没有选择警务室，显示所有社区
                        return communityOptions.value
                    }

                    // 选择了警务室，只显示对应警务室下的社区
                    const filteredOptions = new Set()
                    selectedCounty.value.forEach(county => {
                        selectedTown.value.forEach(town => {
                            selectedPoliceStation.value.forEach(station => {
                                const stationKey = `${county}_${town}_${station}`
                                const stationCommunities = placeHierarchyData.value.station_community_mapping?.[stationKey] || []
                                stationCommunities.forEach(item => {
                                    filteredOptions.add(JSON.stringify(item))
                                })
                            })
                        })
                    })

                    return Array.from(filteredOptions).map(item => JSON.parse(item))
                })
                const md = window.markdownit({
                    html: true,  // 允许HTML标签
                    linkify: true,
                    typographer: true
                });
                
                const { proxy } = getCurrentInstance(); // 获取当前组件实例的代理

                // 添加插件，使所有链接在新标签页打开
                const defaultRender = md.renderer.rules.link_open || function(tokens, idx, options, env, self) {
                    return self.renderToken(tokens, idx, options);
                };
                
                md.renderer.rules.link_open = function(tokens, idx, options, env, self) {
                    // 为所有链接添加 target="_blank" 属性
                    // tokens[idx].attrPush(['target', '_blank']); // 注释掉或删除此行以实现原页面跳转
                    // 添加安全属性
                    tokens[idx].attrPush(['rel', 'noopener noreferrer']);

                    // 检查是否是报告链接
                    const hrefIndex = tokens[idx].attrIndex('href');
                    if (hrefIndex >= 0) {
                        const hrefValue = tokens[idx].attrs[hrefIndex][1];
                        if (hrefValue && hrefValue.startsWith('/view_report/')) {
                            tokens[idx].attrPush(['class', 'report-link']);
                            // 为这类链接添加 onclick 事件来保存历史
                            tokens[idx].attrPush(['onclick', 'saveChatHistoryForReportLink()']);
                        }
                    }
                    
                    return defaultRender(tokens, idx, options, env, self);
                };
                
                // 缓存已处理的消息，避免重复处理
                const processedMessages = new Map();

                // 存储数据预览的查询参数，按消息索引
                const dataPreviewParams = new Map();

                // 处理Markdown格式的链接和报告按钮
                const processReportLinks = (text) => {
                    if (!text) return '';

                    let messageText = String(text);

                    // 检查是否包含HTML内容（查询结果、加载动画等）
                    const htmlPatterns = [
                        '<div class="query-result-header">',
                        '<div class="loading-spinner">',
                        '<div class="report-link-card">'
                    ];

                    let htmlPattern = null;
                    for (const pattern of htmlPatterns) {
                        if (messageText.includes(pattern)) {
                            htmlPattern = pattern;
                            break;
                        }
                    }

                    if (htmlPattern) {
                        // 找到HTML开始和结束位置
                        const htmlStart = messageText.indexOf(htmlPattern);
                        const htmlEnd = messageText.lastIndexOf('</div>') + 6; // 包含</div>

                        if (htmlStart !== -1 && htmlEnd > htmlStart) {
                            // 分离三部分：前面的文本、HTML内容、后面的文本
                            const beforeHtml = messageText.substring(0, htmlStart);
                            const htmlContent = messageText.substring(htmlStart, htmlEnd);
                            const afterHtml = messageText.substring(htmlEnd);

                            let result = '';

                            // 处理HTML前的文本
                            if (beforeHtml.trim()) {
                                result += md.render(beforeHtml);
                            }

                            // 直接添加HTML内容
                            result += htmlContent;

                            // 处理HTML后的文本
                            if (afterHtml.trim()) {
                                result += md.render(afterHtml);
                            }

                            return result;
                        }
                    }

                    // 检查是否包含报告按钮标记
                    const reportButtonMatch = messageText.match(/\*\*\[REPORT_BUTTON:([^\]]+)\]\*\*/);

                    if (reportButtonMatch) {
                        const reportId = reportButtonMatch[1];

                        // 移除按钮标记
                        messageText = messageText.replace(/\*\*\[REPORT_BUTTON:[^\]]+\]\*\*/, '');

                        // 先渲染Markdown
                        let renderedHtml = md.render(messageText);

                        // 添加报告按钮HTML
                        const buttonHtml = `
                            <div class="report-buttons">
                                <a href="/view_report/${reportId}" class="report-button view-report" onclick="saveChatHistoryForReportLink()">
                                    <i class="fas fa-eye"></i>
                                    查看报告
                                </a>
                            </div>
                        `;

                        return renderedHtml + buttonHtml;
                    }

                    return md.render(messageText);
                };

                // 获取处理后的消息，避免重复处理
                const getProcessedMessage = (content, index) => {
                    const cacheKey = `${index}-${content}`;
                    if (processedMessages.has(cacheKey)) {
                        return processedMessages.get(cacheKey);
                    }

                    let result = content;

                    // 先处理数据预览卡片，在Markdown渲染之前
                    // 尝试多种可能的格式
                    const dataPreviewPatterns = [
                        /\*\*\[DATA_PREVIEW:(.*?)\]\*\*/g,  // **[DATA_PREVIEW:...]**
                        /\[DATA_PREVIEW:(.*?)\]/g,          // [DATA_PREVIEW:...]
                        /DATA_PREVIEW:(.*?)(?=\n|$)/g       // DATA_PREVIEW:... (到行尾)
                    ];

                    let foundMatch = false;
                    for (const pattern of dataPreviewPatterns) {
                        result = result.replace(pattern, (match, dataStr) => {
                            try {
                                const data = JSON.parse(dataStr);

                                // 存储查询参数
                                dataPreviewParams.set(index, data.query_params);

                                const cardHtml = generateDataPreviewCard(data, index);
                                foundMatch = true;
                                return cardHtml;
                            } catch (e) {
                                console.error('解析数据预览失败:', e);
                                console.error('原始数据:', dataStr.substring(0, 500));
                                return `<div class="alert alert-warning">数据预览解析失败: ${e.message}</div>`;
                            }
                        });
                        if (foundMatch) break;
                    }

                    if (!foundMatch) {
                        console.log('未找到数据预览匹配（index）：', index);

                        // 如果没有找到数据预览匹配，但消息内容包含查询结果，尝试从localStorage恢复参数
                        if (text.includes('查询完成') || text.includes('条记录') || text.includes('条数据')) {
                            try {
                                const savedQueryState = localStorage.getItem('chat_query_state');
                                if (savedQueryState) {
                                    const queryState = JSON.parse(savedQueryState);
                                    if (queryState.dataPreviewParams && queryState.dataPreviewParams[index]) {
                                        dataPreviewParams.set(index, queryState.dataPreviewParams[index]);
                                        console.log('从localStorage恢复了消息索引', index, '的查询参数');

                                        // 尝试重新生成数据预览卡片
                                        if (queryState.lastQueryParams) {
                                            const mockData = {
                                                total_records: 0, // 从消息文本中提取
                                                sample_data: [],
                                                has_full_data: true,
                                                query_params: queryState.dataPreviewParams[index]
                                            };

                                            // 从消息文本中提取记录数
                                            const recordMatch = text.match(/(\d+)\s*条/);
                                            if (recordMatch) {
                                                mockData.total_records = parseInt(recordMatch[1]);
                                            }

                                            const cardHtml = generateDataPreviewCard(mockData, index);
                                            result = result.replace(/查询完成[^<]*/, cardHtml);
                                        }
                                    }
                                }
                            } catch (e) {
                                console.error('恢复查询参数失败:', e);
                            }
                        }
                    }

                    // 然后处理报告链接和Markdown
                    result = processReportLinks(result);


                    processedMessages.set(cacheKey, result);
                    return result;
                };

                // 生成数据预览卡片HTML
                const generateDataPreviewCard = (data, messageIndex) => {
                    const { total_records, sample_data, has_full_data, query_params } = data;

                    let sampleHtml = '';
                    if (sample_data && sample_data.length > 0) {
                        sampleHtml = sample_data.map((record, index) => {
                            let recordInfo = `<div class="data-record-item">`;
                            recordInfo += `<span class="record-index">${index + 1}.</span>`;
                            if (record.bjsj) recordInfo += `<span class="record-field">时间：${record.bjsj}</span>`;
                            if (record.county_feedback) recordInfo += `<span class="record-field">区县：${record.county_feedback}</span>`;
                            if (record.town) recordInfo += `<span class="record-field">街道：${record.town}</span>`;
                            if (record.community) recordInfo += `<span class="record-field">社区：${record.community}</span>`;
                            if (record.police_situation_category) recordInfo += `<span class="record-field">类别：${record.police_situation_category}</span>`;
                            if (record.police_situation_type) recordInfo += `<span class="record-field">类型：${record.police_situation_type}</span>`;
                            recordInfo += `</div>`;
                            return recordInfo;
                        }).join('');
                    }

                    return `
                        <div class="query-result-header">
                            <i class="fas fa-database"></i>
                            <h4>查询结果</h4>
                            <span class="total-count">共 ${total_records} 条记录</span>
                        </div>

                        ${sample_data && sample_data.length > 0 ? `
                        <div class="query-result-content">
                            <h5>数据示例（前${sample_data.length}条）：</h5>
                            <div class="data-records">
                                ${sampleHtml}
                            </div>
                        </div>
                        ` : ''}

                        <div class="query-result-actions">
                            <button class="btn btn-secondary" onclick="window.vueApp.handleDataAction('export', ${messageIndex})" title="导出全量数据到Excel文件">
                                <i class="fas fa-file-excel"></i>
                                <span>导出Excel</span>
                                <small>(${total_records}条)</small>
                            </button>
                            <button class="btn btn-info" onclick="window.vueApp.handleDataAction('report', ${messageIndex})" title="基于查询数据生成专业分析报告">
                                <i class="fas fa-chart-bar"></i>
                                <span>生成分析报告</span>
                            </button>
                        </div>
                    `;
                };

                // 进度控制函数
                const startProgress = async (text = '正在处理...') => {
                    showProgress.value = true;
                    progressText.value = text;
                    progressPercentage.value = 0;
                    progressStatus.value = '';
                    progressSteps.value = [];

                    // 等待DOM更新后滚动到底部
                    await nextTick();
                    scrollToBottom();
                };

                const updateProgress = async (percentage, text, status = '', steps = []) => {
                    progressPercentage.value = percentage;
                    progressText.value = text;
                    progressStatus.value = status;
                    if (steps.length > 0) {
                        progressSteps.value = steps;
                    }

                    // 等待DOM更新后滚动到底部
                    await nextTick();
                    scrollToBottom();
                };

                const stopProgress = async () => {
                    showProgress.value = false;
                    progressPercentage.value = 0;
                    progressSteps.value = [];

                    // 等待DOM更新后滚动到底部
                    await nextTick();
                    scrollToBottom();
                };
                


                // 禁用日期函数 - 不能选择未来的日期
                const disabledDate = (time) => {
                    return time.getTime() > Date.now()
                }
                
                // 发送消息
                const sendMessage = async () => {
                    if (!inputMessage.value.trim() || isLoading.value) return

                    const userMsg = inputMessage.value.trim()
                    messages.value.push({ role: 'user', content: userMsg })
                    inputMessage.value = ''

                    // 滚动到底部
                    await nextTick()
                    scrollToBottom()

                    // 设置加载状态
                    isLoading.value = true

                    // 检查是否是报告生成请求
                    const isReportRequest = /生成.*报告|分析.*报告|报告.*生成|制作.*报告/.test(userMsg);

                    if (isReportRequest) {
                        // 启动进度显示
                        await startProgress('正在分析您的需求...');

                        // 模拟进度更新
                        const progressSteps = [
                            { icon: 'fas fa-search', text: '分析用户需求', completed: false, current: true },
                            { icon: 'fas fa-database', text: '查询相关数据', completed: false, current: false },
                            { icon: 'fas fa-chart-bar', text: '数据分析处理', completed: false, current: false },
                            { icon: 'fas fa-file-alt', text: '生成分析报告', completed: false, current: false }
                        ];

                        await updateProgress(10, '正在分析您的需求...', '', progressSteps);

                        // 模拟进度更新
                        setTimeout(async () => {
                            progressSteps[0].completed = true;
                            progressSteps[0].current = false;
                            progressSteps[1].current = true;
                            await updateProgress(30, '正在查询相关数据...', '', progressSteps);
                        }, 1000);

                        setTimeout(async () => {
                            progressSteps[1].completed = true;
                            progressSteps[1].current = false;
                            progressSteps[2].current = true;
                            await updateProgress(60, '正在进行数据分析...', '', progressSteps);
                        }, 3000);

                        setTimeout(async () => {
                            progressSteps[2].completed = true;
                            progressSteps[2].current = false;
                            progressSteps[3].current = true;
                            await updateProgress(90, '正在生成分析报告...', '', progressSteps);
                        }, 5000);
                    }

                    try {
                        
                        // 使用fetch API替代axios，以支持流式响应
                        const response = await fetch('/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ message: userMsg })
                        })
                        
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`)
                        }
                        
                        // 检查响应是否为流式数据
                        const contentType = response.headers.get('Content-Type');
                        if (contentType && contentType.includes('text/event-stream')) {
                            // 处理SSE流 - 添加一个空的助手消息用于流式更新
                            messages.value.push({ role: 'assistant', content: '' })
                            const assistantMessageIndex = messages.value.length - 1

                            const reader = response.body.getReader()
                            const decoder = new TextDecoder()

                            while (true) {
                                const { done, value } = await reader.read()
                                if (done) break

                                // 解码接收到的数据
                                const chunk = decoder.decode(value, { stream: true })

                                // 更新消息内容
                                messages.value[assistantMessageIndex].content += chunk

                                // 更新DOM，滚动到底部
                                nextTick(() => {
                                    scrollToBottom()
                                })
                            }
                        } else {
                            // 处理常规JSON响应
                            const data = await response.json()

                            if (data && (data.success === true || data.status === 'success')) {
                                // 如果是报告请求，完成进度
                                if (isReportRequest) {
                                    await updateProgress(100, '报告生成完成！', 'success');
                                    setTimeout(async () => {
                                        await stopProgress();
                                    }, 1000);
                                }

                                // 成功响应，提取消息内容
                                let responseText = data.message || data.response || '收到您的消息'

                                // 检查是否有报告内容，如果有则添加按钮标记
                                if (data.report_content && data.report_id) {
                                    console.log('检测到报告生成，报告ID:', data.report_id); // 调试日志
                                    responseText += `\n\n**[REPORT_BUTTON:${data.report_id}]**`;
                                }

                                messages.value.push({ role: 'assistant', content: responseText })
                            } else {
                                // 错误响应
                                if (isReportRequest) {
                                    await stopProgress();
                                }
                                const errorText = data.message || data.error || '抱歉，处理您的请求时出现了问题。'
                                messages.value.push({ role: 'assistant', content: errorText })
                            }
                        }
                    } catch (error) {
                        // 网络错误时添加错误消息
                        if (isReportRequest) {
                            await stopProgress();
                        }
                        messages.value.push({ role: 'assistant', content: '抱歉，发生了网络错误，请稍后再试。' })
                    } finally {
                        isLoading.value = false
                        // 滚动到底部
                        await nextTick()
                        scrollToBottom()
                    }
                }

                // 发送结构化查询（智能标签选择器专用）
                const sendStructuredQuery = async (displayText, queryParams) => {
                    if (isLoading.value) return

                    // 添加用户消息到聊天界面
                    messages.value.push({ role: 'user', content: displayText })

                    // 滚动到底部
                    await nextTick()
                    scrollToBottom()

                    // 设置加载状态
                    isLoading.value = true

                    try {
                        const response = await fetch('/api/structured_query', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                display_text: displayText,
                                query_params: queryParams
                            })
                        })

                        if (response.ok) {
                            const data = await response.json()
                            if (data.success) {
                                // 添加助手回复
                                let responseText = data.message || '查询完成'

                                // 如果有数据，显示数据预览卡片
                                if (data.data && data.total_records !== undefined) {
                                    // 简化数据，避免JSON过大
                                    const previewData = {
                                        total_records: data.total_records,
                                        sample_data: data.data.slice(0, 5), // 减少到5条
                                        has_full_data: data.data.length > 0,
                                        query_params: queryParams
                                    };

                                    try {
                                        // 使用JSON标记方式，让getProcessedMessage函数处理
                                        responseText += `\n\n**[DATA_PREVIEW:${JSON.stringify(previewData)}]**`;
                                    } catch (e) {
                                        console.error('生成数据预览卡片失败:', e);
                                        responseText += `\n\n<div class="alert alert-info">查询完成，共找到 ${data.total_records} 条记录</div>`;
                                    }
                                }

                                messages.value.push({ role: 'assistant', content: responseText })
                            } else {
                                // 错误响应
                                const errorText = data.message || data.error || '抱歉，处理您的查询时出现了问题。'
                                messages.value.push({ role: 'assistant', content: errorText })
                            }
                        } else {
                            messages.value.push({ role: 'assistant', content: '抱歉，查询请求失败，请稍后再试。' })
                        }
                    } catch (error) {
                        // 网络错误时添加错误消息
                        messages.value.push({ role: 'assistant', content: '抱歉，发生了网络错误，请稍后再试。' })
                    } finally {
                        isLoading.value = false
                        // 滚动到底部
                        await nextTick()
                        scrollToBottom()
                    }
                }

                // 显示使用指南
                const showUsageGuide = () => {
                    usageGuideVisible.value = true
                }

                // 显示标签选择器
                const showTagSelector = async () => {
                    tagSelectorVisible.value = true
                    await Promise.all([
                        loadTagHierarchy(),
                        loadPlaceHierarchy(),
                        loadPlaceOccurTags(),
                        loadPlaceCategoryHierarchy()
                    ])
                }

                // 加载标签层级数据
                const loadTagHierarchy = async () => {
                    if (level1Options.value.length > 0) return // 已加载过

                    tagLoading.value = true
                    try {
                        const response = await fetch('/api/tags/hierarchical')
                        if (response.ok) {
                            const data = await response.json()
                            if (data.success) {
                                tagHierarchyData.value = data.data
                                level1Options.value = data.data.level1_options || []
                            } else {
                                ElementPlus.ElMessage.error('加载标签数据失败: ' + data.message)
                            }
                        } else {
                            ElementPlus.ElMessage.error('加载标签数据失败')
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('加载标签数据时发生网络错误')
                    } finally {
                        tagLoading.value = false
                    }
                }

                // 加载地址标签层级数据
                const loadPlaceHierarchy = async () => {
                    if (countyOptions.value.length > 0) return // 已加载过

                    try {
                        const response = await fetch('/api/tags/place-hierarchy')
                        if (response.ok) {
                            const data = await response.json()
                            if (data.success) {
                                placeHierarchyData.value = data.data
                                countyOptions.value = data.data.county_options || []
                            } else {
                                ElementPlus.ElMessage.error('加载地址标签数据失败: ' + data.message)
                            }
                        } else {
                            ElementPlus.ElMessage.error('加载地址标签数据失败')
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('加载地址标签数据时发生网络错误')
                    }
                }

                // 加载发生处所标签
                const loadPlaceOccurTags = async () => {
                    if (placeOccurOptions.value.length > 0) return // 已加载过

                    try {
                        const response = await fetch('/api/tags/place-occur')
                        if (response.ok) {
                            const data = await response.json()
                            if (data.success) {
                                placeOccurOptions.value = data.data || []
                            } else {
                                ElementPlus.ElMessage.error('加载发生处所标签失败: ' + data.message)
                            }
                        } else {
                            ElementPlus.ElMessage.error('加载发生处所标签失败')
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('加载发生处所标签时发生网络错误')
                    }
                }

                // 加载场所类型标签层级数据
                const loadPlaceCategoryHierarchy = async () => {
                    if (placeCategoryOptions.value.length > 0) return // 已加载过

                    try {
                        const response = await fetch('/api/tags/place-category-hierarchy')
                        if (response.ok) {
                            const data = await response.json()
                            if (data.success) {
                                placeCategoryHierarchyData.value = data.data
                                placeCategoryOptions.value = data.data.category_options || []
                            } else {
                                ElementPlus.ElMessage.error('加载场所类型标签数据失败: ' + data.message)
                            }
                        } else {
                            ElementPlus.ElMessage.error('加载场所类型标签数据失败')
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('加载场所类型标签数据时发生网络错误')
                    }
                }

                // 一级标签变化处理
                const onLevel1Change = (values) => {
                    // 清空下级选择
                    selectedLevel2.value = []
                    selectedLevel3.value = []
                    level2Options.value = []
                    level3Options.value = []

                    // 收集所有选中一级标签对应的二级标签
                    if (values && values.length > 0 && tagHierarchyData.value.level2_mapping) {
                        const allLevel2Options = new Set()
                        values.forEach(level1 => {
                            const level2List = tagHierarchyData.value.level2_mapping[level1] || []
                            level2List.forEach(item => allLevel2Options.add(JSON.stringify(item)))
                        })
                        level2Options.value = Array.from(allLevel2Options).map(item => JSON.parse(item))
                    }
                }

                // 二级标签变化处理
                const onLevel2Change = (values) => {
                    // 清空三级选择
                    selectedLevel3.value = []
                    level3Options.value = []

                    // 收集所有选中二级标签对应的三级标签
                    if (values && values.length > 0 && tagHierarchyData.value.level3_mapping) {
                        const allLevel3Options = new Set()
                        values.forEach(level2 => {
                            const level3List = tagHierarchyData.value.level3_mapping[level2] || []
                            level3List.forEach(item => allLevel3Options.add(JSON.stringify(item)))
                        })
                        level3Options.value = Array.from(allLevel3Options).map(item => JSON.parse(item))
                    }
                }

                // 县区变化处理
                const onCountyChange = (values) => {
                    // 清空下级选择
                    selectedTown.value = []
                    selectedPoliceStation.value = []
                    selectedCommunity.value = []
                    townOptions.value = []
                    policeStationOptions.value = []
                    communityOptions.value = []

                    // 收集所有选中县区对应的街道
                    if (values && values.length > 0 && placeHierarchyData.value.town_mapping) {
                        const allTownOptions = new Set()
                        values.forEach(county => {
                            const townList = placeHierarchyData.value.town_mapping[county] || []
                            townList.forEach(item => allTownOptions.add(JSON.stringify(item)))
                        })
                        townOptions.value = Array.from(allTownOptions).map(item => JSON.parse(item))
                    }
                }

                // 街道变化处理
                const onTownChange = (values) => {
                    // 清空下级选择
                    selectedPoliceStation.value = []
                    selectedCommunity.value = []
                    policeStationOptions.value = []
                    communityOptions.value = []

                    if (values && values.length > 0) {
                        // 收集所有选中街道对应的警务室
                        if (placeHierarchyData.value.police_station_mapping) {
                            const allPoliceStationOptions = new Set()
                            selectedCounty.value.forEach(county => {
                                values.forEach(town => {
                                    const key = `${county}_${town}`
                                    const stationList = placeHierarchyData.value.police_station_mapping[key] || []
                                    stationList.forEach(item => allPoliceStationOptions.add(JSON.stringify(item)))
                                })
                            })
                            policeStationOptions.value = Array.from(allPoliceStationOptions).map(item => JSON.parse(item))
                        }

                        // 收集所有选中街道对应的社区
                        if (placeHierarchyData.value.community_mapping) {
                            const allCommunityOptions = new Set()
                            selectedCounty.value.forEach(county => {
                                values.forEach(town => {
                                    const key = `${county}_${town}`
                                    const communityList = placeHierarchyData.value.community_mapping[key] || []
                                    communityList.forEach(item => allCommunityOptions.add(JSON.stringify(item)))
                                })
                            })
                            communityOptions.value = Array.from(allCommunityOptions).map(item => JSON.parse(item))
                        }
                    }
                }

                // 警务室变化处理
                const onPoliceStationChange = (values) => {
                    // 当选择警务室时，清空已选择的社区（因为社区选项会被过滤）
                    selectedCommunity.value = []
                    // 注意：不清空communityOptions，因为计算属性会自动处理过滤
                }

                // 场所类别变化处理
                const onPlaceCategoryChange = (values) => {
                    // 清空场所大类选择
                    selectedPlaceMainCategory.value = []
                    placeMainCategoryOptions.value = []

                    // 收集所有选中场所类别对应的场所大类
                    if (values && values.length > 0 && placeCategoryHierarchyData.value.main_category_mapping) {
                        const allMainCategoryOptions = new Set()
                        values.forEach(category => {
                            const mainCategoryList = placeCategoryHierarchyData.value.main_category_mapping[category] || []
                            mainCategoryList.forEach(item => allMainCategoryOptions.add(JSON.stringify(item)))
                        })
                        placeMainCategoryOptions.value = Array.from(allMainCategoryOptions).map(item => JSON.parse(item))
                    }
                }

                // 应用标签选择
                const applyTagSelection = () => {
                    // 构建结构化查询参数
                    const queryParams = {
                        time_range: null,
                        location: {},
                        situation_type: [],
                        place_occur: [],
                        place_category: []
                    }

                    // 构建查询文本用于显示
                    let queryParts = []

                    // 处理时间范围
                    if (selectedTimeRange.value && selectedTimeRange.value.length === 2) {
                        queryParams.time_range = {
                            start_date: selectedTimeRange.value[0],
                            end_date: selectedTimeRange.value[1],
                            description: `${selectedTimeRange.value[0]} 至 ${selectedTimeRange.value[1]}`
                        }
                        queryParts.push(`时间范围从${selectedTimeRange.value[0]}到${selectedTimeRange.value[1]}`)
                    }

                    // 处理警情类型（优先使用最具体的分类）
                    if (selectedLevel3.value && selectedLevel3.value.length > 0) {
                        queryParams.situation_type = selectedLevel3.value
                        if (selectedLevel3.value.length === 1) {
                            queryParts.push(`警情类型为${selectedLevel3.value[0]}`)
                        } else {
                            queryParts.push(`警情类型包括${selectedLevel3.value.join('、')}`)
                        }
                    } else if (selectedLevel2.value && selectedLevel2.value.length > 0) {
                        queryParams.situation_type = selectedLevel2.value
                        if (selectedLevel2.value.length === 1) {
                            queryParts.push(`警情类型为${selectedLevel2.value[0]}`)
                        } else {
                            queryParts.push(`警情类型包括${selectedLevel2.value.join('、')}`)
                        }
                    } else if (selectedLevel1.value && selectedLevel1.value.length > 0) {
                        queryParams.situation_type = selectedLevel1.value
                        if (selectedLevel1.value.length === 1) {
                            queryParts.push(`警情类型为${selectedLevel1.value[0]}`)
                        } else {
                            queryParts.push(`警情类型包括${selectedLevel1.value.join('、')}`)
                        }
                    }

                    // 处理地址标签
                    let addressParts = []
                    if (selectedCommunity.value && selectedCommunity.value.length > 0) {
                        queryParams.location.community = selectedCommunity.value
                        addressParts.push(`社区为${selectedCommunity.value.join('、')}`)
                    }
                    if (selectedPoliceStation.value && selectedPoliceStation.value.length > 0) {
                        queryParams.location.police_station = selectedPoliceStation.value
                        if (!addressParts.length) addressParts.push(`警务室为${selectedPoliceStation.value.join('、')}`)
                    }
                    if (selectedTown.value && selectedTown.value.length > 0) {
                        queryParams.location.town = selectedTown.value
                        if (!addressParts.length) addressParts.push(`街道为${selectedTown.value.join('、')}`)
                    }
                    if (selectedCounty.value && selectedCounty.value.length > 0) {
                        queryParams.location.county = selectedCounty.value
                        if (!addressParts.length) addressParts.push(`县区为${selectedCounty.value.join('、')}`)
                    }
                    if (addressParts.length > 0) {
                        queryParts.push(addressParts.join('，'))
                    }

                    // 处理发生处所
                    if (selectedPlaceOccur.value && selectedPlaceOccur.value.length > 0) {
                        queryParams.place_occur = selectedPlaceOccur.value
                        if (selectedPlaceOccur.value.length === 1) {
                            queryParts.push(`发生处所为${selectedPlaceOccur.value[0]}`)
                        } else {
                            queryParts.push(`发生处所包括${selectedPlaceOccur.value.join('、')}`)
                        }
                    }

                    // 处理场所类型（优先使用场所大类）
                    if (selectedPlaceMainCategory.value && selectedPlaceMainCategory.value.length > 0) {
                        queryParams.place_category = selectedPlaceMainCategory.value.map(cat => ({type: 'main_category', value: cat}))
                        if (selectedPlaceMainCategory.value.length === 1) {
                            queryParts.push(`场所大类为${selectedPlaceMainCategory.value[0]}`)
                        } else {
                            queryParts.push(`场所大类包括${selectedPlaceMainCategory.value.join('、')}`)
                        }
                    } else if (selectedPlaceCategory.value && selectedPlaceCategory.value.length > 0) {
                        queryParams.place_category = selectedPlaceCategory.value.map(cat => ({type: 'category', value: cat}))
                        if (selectedPlaceCategory.value.length === 1) {
                            queryParts.push(`场所类别为${selectedPlaceCategory.value[0]}`)
                        } else {
                            queryParts.push(`场所类别包括${selectedPlaceCategory.value.join('、')}`)
                        }
                    }

                    if (queryParts.length > 0) {
                        const queryText = `查询${queryParts.join('，')}的数据`

                        // 关闭标签选择器
                        tagSelectorVisible.value = false

                        // 清空选择
                        selectedTimeRange.value = []
                        selectedLevel1.value = []
                        selectedLevel2.value = []
                        selectedLevel3.value = []
                        level2Options.value = []
                        level3Options.value = []

                        // 清空地址标签选择
                        selectedCounty.value = []
                        selectedTown.value = []
                        selectedPoliceStation.value = []
                        selectedCommunity.value = []
                        townOptions.value = []
                        policeStationOptions.value = []
                        communityOptions.value = []

                        // 清空发生处所选择
                        selectedPlaceOccur.value = []

                        // 清空场所类型选择
                        selectedPlaceMainCategory.value = []
                        selectedPlaceCategory.value = []
                        placeCategoryOptions.value = []

                        // 直接发送结构化查询请求，不在输入框显示
                        sendStructuredQuery(queryText, queryParams)
                    } else {
                        // 如果没有选择任何条件，提示用户
                        ElementPlus.ElMessage.warning('请至少选择一个查询条件')
                    }
                }
                
                // 清空对话
                const clearChat = async () => {
                    messages.value = [] // 清空前端显示
                    try {
                        const response = await fetch('/clear_conversation', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        if (response.ok) {
                            const data = await response.json();
                            if (data.status === 'success') {
                                ElementPlus.ElMessage.success('会话已清空');
                            } else {
                                ElementPlus.ElMessage.error('清空会话失败: ' + data.message);
                            }
                        } else {
                            ElementPlus.ElMessage.error('清空会话失败，网络错误');
                        }
                    } catch (error) {
                        ElementPlus.ElMessage.error('清空会话时发生连接错误');
                    }
                }
                
                // 滚动到底部
                const scrollToBottom = () => {
                    if (messagesContainer.value) {
                        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
                    }
                }
                
                // Helper function to load conversation history
                const loadConversationHistory = async () => {
                    let historyLoadedFromServer = false;
                    try {
                        const response = await fetch('/get_conversation');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.status === 'success' && data.conversation && data.conversation.length > 0) {
                                messages.value = data.conversation;
                                localStorage.removeItem('chat_history');
                                historyLoadedFromServer = true;
                            }
                        }
                    } catch (error) {
                        // 静默处理错误，继续尝试从localStorage加载
                    }

                    // If history was not loaded from server, try localStorage
                    if (!historyLoadedFromServer) {
                        const savedHistory = localStorage.getItem('chat_history');
                        if (savedHistory) {
                            try {
                                const parsedHistory = JSON.parse(savedHistory);
                                if (Array.isArray(parsedHistory) && parsedHistory.length > 0) {
                                    messages.value = parsedHistory;
                                }
                            } catch (e) {
                                // 静默处理解析错误
                            } finally {
                                localStorage.removeItem('chat_history');
                            }
                        }
                    }

                    // 恢复查询状态
                    try {
                        const savedQueryState = localStorage.getItem('chat_query_state');
                        if (savedQueryState) {
                            const queryState = JSON.parse(savedQueryState);
                            if (queryState.lastQueryParams) {
                                lastQueryParams.value = queryState.lastQueryParams;
                            }
                            // 恢复dataPreviewParams
                            if (queryState.dataPreviewParams) {
                                // 将对象转换回Map
                                for (const [key, value] of Object.entries(queryState.dataPreviewParams)) {
                                    dataPreviewParams.set(parseInt(key), value);
                                }

                                // 重新处理包含查询结果但缺少数据预览卡片的消息
                                setTimeout(() => {
                                    messages.value.forEach((message, index) => {
                                        if (message.role === 'assistant' &&
                                            (message.content.includes('查询完成') || message.content.includes('条记录') || message.content.includes('条数据')) &&
                                            !message.content.includes('query-result-header') &&
                                            queryState.dataPreviewParams[index]) {

                                            console.log('重新处理消息索引', index, '的查询结果');

                                            // 构造数据预览数据
                                            const recordMatch = message.content.match(/(\d+)\s*条/);
                                            const totalRecords = recordMatch ? parseInt(recordMatch[1]) : 0;

                                            const mockData = {
                                                total_records: totalRecords,
                                                sample_data: [],
                                                has_full_data: true,
                                                query_params: queryState.dataPreviewParams[index]
                                            };

                                            const cardHtml = generateDataPreviewCard(mockData, index);
                                            message.content = message.content.replace(/查询完成[^<]*/, cardHtml);
                                        }
                                    });

                                    // 清理localStorage
                                    localStorage.removeItem('chat_query_state');
                                }, 500);
                            }
                        }
                    } catch (e) {
                        // 静默处理解析错误
                        console.error('恢复查询状态失败:', e);
                    }
                    
                    // Return true if messages array has content, false otherwise
                    return messages.value.length > 0;
                };

                // 初始化
                onMounted(async () => {
                    // 挂载Vue实例到window，以便外部函数可以调用其方法
                    window.vueAppInstance = proxy; 
                    
                    const historyActuallyLoaded = await loadConversationHistory();
                    
                    if (historyActuallyLoaded) {
                        await nextTick(); // Wait for DOM to update with messages
                    }
                    scrollToBottom(); // Scroll to bottom after history loading attempt
                });
                
                // 在Vue实例销毁前清理
                onBeforeUnmount(() => { // 使用导入的 onBeforeUnmount
                    if (window.vueAppInstance === proxy) {
                        window.vueAppInstance = null;
                    }
                });
                
                // 将保存历史的逻辑移到Vue实例的方法中
                const saveCurrentChatHistory = () => {
                    localStorage.setItem('chat_history', JSON.stringify(messages.value));
                    // 同时保存当前的查询状态，以便返回后恢复
                    const currentState = {
                        lastQueryParams: lastQueryParams.value,
                        dataPreviewParams: Object.fromEntries(dataPreviewParams), // 将Map转换为对象保存
                        hasQueryResult: messages.value.some(msg =>
                            msg.role === 'assistant' &&
                            (msg.content.includes('**[DATA_PREVIEW:') || msg.content.includes('查询完成'))
                        )
                    };
                    localStorage.setItem('chat_query_state', JSON.stringify(currentState));
                };

                // 数据预览相关方法
                const showFullDataTable = (data) => {
                    currentFullData.value = data
                    tableCurrentPage.value = 1
                    fullDataTableVisible.value = true
                }

                const closeFullDataTable = () => {
                    fullDataTableVisible.value = false
                    currentFullData.value = []
                }

                // 导出Excel数据
                const exportToExcel = async (queryParams) => {
                    try {
                        // 显示导出开始消息
                        ElementPlus.ElMessage.info({
                            message: '正在准备Excel文件，请稍候...',
                            duration: 2000
                        })

                        const response = await fetch('/api/export_excel', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                query_params: queryParams
                            })
                        })

                        const result = await response.json()

                        if (result.success) {
                            // 显示成功消息
                            ElementPlus.ElMessage.success({
                                message: result.message,
                                duration: 3000
                            })

                            // 延迟一下再开始下载，让用户看到成功消息
                            setTimeout(() => {
                                window.location.href = result.download_url
                            }, 500)

                            // 在聊天中添加成功消息
                            const successMessage = {
                                role: 'assistant',
                                content: `📊 ${result.message}，文件将自动开始下载。`
                            }
                            messages.value.push(successMessage)
                        } else {
                            ElementPlus.ElMessage.error({
                                message: result.message,
                                duration: 4000
                            })

                            // 在聊天中添加错误消息
                            messages.value.push({
                                role: 'assistant',
                                content: `❌ Excel导出失败：${result.message}`
                            })
                        }
                    } catch (error) {
                        console.error('Excel导出失败:', error)
                        ElementPlus.ElMessage.error({
                            message: '网络错误，请稍后再试',
                            duration: 4000
                        })

                        // 在聊天中添加错误消息
                        messages.value.push({
                            role: 'assistant',
                            content: '❌ Excel导出时发生网络错误，请稍后再试。'
                        })
                    } finally {
                        // 滚动到底部
                        await nextTick()
                        scrollToBottom()
                    }
                }

                // 处理数据操作（导出、全量数据、分析报告）
                const handleDataAction = (action, messageIndex) => {
                    const queryParams = dataPreviewParams.get(messageIndex);
                    if (!queryParams) {
                        console.error('未找到消息索引对应的查询参数:', messageIndex);
                        ElementPlus.ElMessage.error('操作失败：未找到查询参数');
                        return;
                    }

                    switch (action) {
                        case 'export':
                            exportToExcel(queryParams);
                            break;
                        case 'fulldata':
                            getFullData(queryParams);
                            break;
                        case 'report':
                            generateAnalysisReport([], queryParams);
                            break;
                        default:
                            console.error('未知的数据操作:', action);
                    }
                };

                // 获取全量数据
                const getFullData = async (queryParams) => {
                    try {
                        // 显示加载状态
                        const loadingMessage = { role: 'assistant', content: '正在获取全量数据，请稍候...' }
                        messages.value.push(loadingMessage)

                        await nextTick()
                        scrollToBottom()

                        const response = await fetch('/api/get_full_data', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                query_params: queryParams
                            })
                        })

                        const result = await response.json()

                        // 移除加载消息
                        messages.value.pop()

                        if (result.success) {
                            // 添加全量数据预览卡片
                            let responseText = result.message

                            try {
                                const previewData = {
                                    total_records: result.total_records,
                                    sample_data: result.data.slice(0, 5),
                                    has_full_data: result.data.length > 0,
                                    query_params: queryParams
                                };

                                // 使用JSON标记方式，让getProcessedMessage函数处理
                                responseText += `\n\n**[DATA_PREVIEW:${JSON.stringify(previewData)}]**`;
                            } catch (e) {
                                console.error('生成全量数据预览卡片失败:', e);
                                responseText += `\n\n<div class="alert alert-info">获取全量数据完成，共 ${result.total_records} 条记录</div>`;
                            }

                            messages.value.push({ role: 'assistant', content: responseText })
                        } else {
                            messages.value.push({ role: 'assistant', content: `获取全量数据失败: ${result.message}` })
                        }

                        await nextTick()
                        scrollToBottom()

                    } catch (error) {
                        // 移除加载消息
                        if (messages.value.length > 0 && messages.value[messages.value.length - 1].content.includes('正在获取全量数据')) {
                            messages.value.pop()
                        }

                        console.error('获取全量数据失败:', error)
                        messages.value.push({ role: 'assistant', content: '获取全量数据失败，网络错误，请稍后再试' })

                        await nextTick()
                        scrollToBottom()
                    }
                }



                const generateAnalysisReport = async (data, queryParams) => {
                    try {
                        // 设置输入框内容并发送消息
                        const reportMessage = `分析以上数据，并生成分析报告`;
                        inputMessage.value = reportMessage;

                        // 调用现有的sendMessage方法，这样可以复用所有的进度显示和错误处理逻辑
                        await sendMessage();

                    } catch (error) {
                        console.error('生成分析报告失败:', error)
                        ElementPlus.ElMessage.error('生成分析报告失败，请稍后再试')
                    }
                }

                return {
                    messages,
                    inputMessage,
                    isLoading,
                    messagesContainer,
                    sendMessage,
                    clearChat,
                    scrollToBottom,
                    processReportLinks,
                    getProcessedMessage,
                    generateDataPreviewCard,
                    saveCurrentChatHistory,
                    // 进度条相关
                    showProgress,
                    progressText,
                    progressPercentage,
                    progressStatus,
                    progressSteps,
                    startProgress,
                    updateProgress,
                    stopProgress,
                    // 弹窗相关
                    usageGuideVisible,
                    tagSelectorVisible,
                    tagLoading,
                    showUsageGuide,
                    showTagSelector,
                    // 标签选择相关
                    selectedTimeRange,
                    selectedLevel1,
                    selectedLevel2,
                    selectedLevel3,
                    level1Options,
                    level2Options,
                    level3Options,
                    onLevel1Change,
                    onLevel2Change,
                    // 地址标签相关
                    selectedCounty,
                    selectedTown,
                    selectedPoliceStation,
                    selectedCommunity,
                    countyOptions,
                    townOptions,
                    policeStationOptions,
                    communityOptions,
                    filteredCommunityOptions,
                    onCountyChange,
                    onTownChange,
                    onPoliceStationChange,
                    // 发生处所相关
                    selectedPlaceOccur,
                    placeOccurOptions,
                    // 场所类型相关
                    selectedPlaceMainCategory,
                    selectedPlaceCategory,
                    placeMainCategoryOptions,
                    placeCategoryOptions,
                    onPlaceCategoryChange,
                    applyTagSelection,
                    disabledDate,
                    // 数据预览相关
                    dataPreviewVisible,
                    currentDataPreview,
                    fullDataTableVisible,
                    currentFullData,
                    tableCurrentPage,
                    tablePageSize,
                    showFullDataTable,
                    closeFullDataTable,
                    exportToExcel,
                    getFullData,
                    generateAnalysisReport,
                    handleDataAction
                }
            }
        }).use(ElementPlus, {
            locale: ElementPlusLocaleZhCn
        })

        const vueInstance = app.mount('#app')

        // 设置全局引用，供HTML onclick事件使用
        window.vueApp = vueInstance

        // 需要在全局作用域或 Vue 实例中可访问的地方定义这个函数
        function saveChatHistoryForReportLink() {
            if (window.vueAppInstance && typeof window.vueAppInstance.saveCurrentChatHistory === 'function') {
                window.vueAppInstance.saveCurrentChatHistory();
            }
        }
    </script>
</body>
</html> 