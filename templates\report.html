<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警情分析智能助手</title>
    <!-- 引入Element Plus CSS -->
    <link rel="stylesheet" href="/static/lib/element-plus/index.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="/static/lib/fontawesome/css/all.min.css">
    <!-- 引入共享样式 -->
    <link rel="stylesheet" href="/static/css/common.css">
    <!-- 引入Highlight.js以显示代码 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/highlight/github.min.css') }}">
    <!-- 引入ECharts来渲染可视化图表 -->
    <script src="{{ url_for('static', filename='lib/echarts/echarts.min.js') }}"></script>
    <style>
        /* 报告页面特定样式 */
        
        /* 报告页面特定基础样式 */
        html, body {
            height: 100%;
            overflow: hidden;
        }

        body {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .app-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            animation: fadeIn 0.6s ease-out;
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            box-sizing: border-box;
            overflow-y: auto;
        }


        
        /* 页面头部 */
        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px 24px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.2), 0 4px 16px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            width: 100%;
            min-height: 0;
        }

        /* 确保卡片撑满容器 */
        .el-card {
            width: 100%;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(14, 165, 233, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(14, 165, 233, 0.2);
            transition: all 0.3s ease;
        }

        .el-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
        }



        .app-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: white;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 16px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .app-title i {
            font-size: 2rem;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
        }
        
        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 报告内容样式 */
        .report-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(14, 165, 233, 0.2);
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .report-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        .report-content {
            padding: 32px;
            font-size: 1.05rem;
            line-height: 1.7;
            position: relative;
            z-index: 1;
            width: 100%;
            max-width: none;
        }
        
        .report-content h1 {
            color: var(--primary-dark);
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 16px;
            margin-bottom: 32px;
            font-weight: 800;
            font-size: 2.2rem;
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .report-content h2 {
            color: var(--text-color);
            border-left: 5px solid var(--primary-color);
            padding-left: 16px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-weight: 700;
            font-size: 1.5rem;
            position: relative;
        }

        .report-content h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 3px;
        }

        .report-content h3 {
            color: var(--text-color);
            margin-top: 32px;
            margin-bottom: 18px;
            font-weight: 600;
            font-size: 1.25rem;
        }
        
        .report-content table {
            width: 100%;
            margin-bottom: 20px;
            border-collapse: collapse;
        }
        
        .report-content th,
        .report-content td {
            border: 1px solid var(--border-color);
            padding: 8px 12px;
            text-align: left;
        }
        
        .report-content th {
            background-color: var(--bg-color);
        }
        
        .report-content tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .report-content blockquote {
            border-left: 4px solid #ddd;
            padding: 10px 15px;
            margin: 15px 0;
            background-color: #f8f9fa;
        }

        /* 统一的按钮基础样式 */
        .input-btn {
            border-radius: 14px;
            padding: 14px 18px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            min-width: 200px;
        }

        .input-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .input-btn:hover::before {
            left: 100%;
        }

        .input-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .input-btn:active {
            transform: translateY(-1px);
            transition: all 0.1s ease;
        }

        .input-btn .fas {
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-btn:hover .fas {
            transform: scale(1.1) rotate(5deg);
        }

        /* 下载按钮 - 主要操作 */
        .download-btn {
            background: linear-gradient(135deg, var(--btn-primary) 0%, var(--btn-primary-dark) 100%);
            color: white;
            border-color: transparent;
        }

        .download-btn:hover {
            background: linear-gradient(135deg, var(--btn-primary-light) 0%, var(--btn-primary) 100%);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
        }

        .download-btn:disabled {
            background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        /* 返回按钮 - 辅助操作 */
        .back-btn {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: var(--btn-secondary-dark);
            border: 2px solid var(--btn-secondary);
            border-color: var(--btn-secondary);
            padding: 8px 16px;
            font-size: 14px;
            min-height: auto;
            min-width: auto;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, var(--btn-secondary) 0%, var(--btn-secondary-dark) 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(2, 132, 199, 0.4);
        }

        /* 操作卡片美化 */
        .operation-card {
            background: linear-gradient(135deg, var(--bg-light) 0%, #e2e8f0 100%);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.1);
        }

        .operation-card .el-card__header {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            border-radius: 15px 15px 0 0;
            border-bottom: none;
            color: white;
        }

        .operation-card .el-card__body {
            padding: 30px;
        }
        
        .sql-section {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .sql-query {
            background-color: #282c34;
            color: #abb2bf;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .report-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: var(--card-shadow);
            margin: 15px 0;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                padding: 15px;
            }

            .app-header {
                padding: 15px 20px;
                margin-bottom: 20px;
            }

            .app-title {
                font-size: 1.5rem;
            }

            .report-content {
                padding: 20px;
                font-size: 1rem;
            }

            .input-btn {
                padding: 10px 14px;
                font-size: 13px;
                min-height: 40px;
            }
        }

        @media (max-width: 480px) {
            .app-container {
                padding: 10px;
            }

            .app-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .report-content {
                padding: 15px;
            }
        }
        

    </style>
</head>

<body>
    <div id="app" class="app-container">
        <!-- 页面头部 -->
        <header class="app-header">
            <h1 class="app-title">
                <i class="fas fa-shield-alt"></i>
                警情分析智能助手
            </h1>
            <div class="header-controls">
                <!-- <el-button @click="toggleTheme" size="small" circle>
                    <i class="fas" :class="[[ isDark ? 'fa-sun' : 'fa-moon' ]]"></i>
                </el-button> -->
                <button @click="goBack" class="input-btn back-btn">
                    <i class="fas fa-arrow-left"></i>
                    <span>返回对话</span>
                </button>
            </div>
        </header>

        <!-- 主内容 -->
        <div class="main-content">
            <!-- 元数据部分 -->
            {% if metadata %}
            <el-card class="mb-4">
                <template #header>
                    <div>
                        <i class="fas fa-info-circle mr-2"></i> 报告概览
                    </div>
                </template>
                
                <!-- 关键指标 -->
                {% if metadata.key_metrics %}
                <el-row :gutter="20" class="mb-3">
                    {% for metric in metadata.key_metrics %}
                    <el-col :span="6" :xs="12" class="mb-3">
                        <el-card shadow="hover" class="text-center">
                            <div style="font-size: 1.8rem; font-weight: bold; color: var(--primary-color);">{{ metric.value }}</div>
                            <div style="color: var(--text-color); font-size: 0.9rem;">{{ metric.label }}</div>
                        </el-card>
                    </el-col>
                    {% endfor %}
                </el-row>
                {% endif %}

                <!-- 时间范围 -->
                {% if metadata.time_range %}
                <div class="mb-3">
                    <h5><i class="fas fa-calendar-alt mr-1"></i> 时间范围</h5>
                    <p class="mb-0">{{ metadata.time_range }}</p>
                    
                    {% if metadata.actual_data_time_range %}
                    <div class="mt-1 text-info">
                        <small><i class="fas fa-info-circle mr-1"></i> 实际数据范围: {{ metadata.actual_data_time_range }}</small>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- 分析维度 -->
                {% if metadata.dimensions %}
                <div class="mb-3">
                    <h5><i class="fas fa-layer-group mr-1"></i> 分析维度</h5>
                    <div>
                        {% for dimension in metadata.dimensions %}
                        <el-tag class="mr-1 mb-1">{{ dimension }}</el-tag>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- 特殊关注点 -->
                {% if metadata.focus_points %}
                <div class="mb-3">
                    <h5><i class="fas fa-search mr-1"></i> 特殊关注点</h5>
                    <ul class="mb-0">
                        {% for point in metadata.focus_points %}
                        <li>{{ point }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
            </el-card>
            {% endif %}

            <!-- SQL查询部分 (如果有) -->
            {% if has_sql %}
            <el-card class="mb-4">
                <template #header>
                    <div>
                        <i class="fas fa-database mr-2"></i> SQL查询
                    </div>
                </template>
                
                <el-collapse>
                    {% for query in sql_queries %}
                    <el-collapse-item title="查询 #{{ loop.index }}{% if query.comment %}: {{ query.comment }}{% endif %}">
                        <div class="mb-2">
                            <el-row type="flex" justify="end" class="mb-2">
                                <el-button size="small" @click="copySql('{{ query.sql | escape }}')">
                                    <i class="fas fa-copy mr-1"></i> 复制
                                </el-button>
                                <el-button size="small" @click="downloadSql({{ loop.index0 }})">
                                    <i class="fas fa-download mr-1"></i> 下载
                                </el-button>
                            </el-row>
                            <pre><code class="language-sql">{{ query.sql }}</code></pre>

                            {% if query.result_preview %}
                            <el-divider content-position="left">查询结果预览</el-divider>
                            <div class="table-responsive">
                                {{ query.result_preview | safe }}
                            </div>
                            {% endif %}
                        </div>
                    </el-collapse-item>
                    {% endfor %}
                </el-collapse>
            </el-card>
            {% endif %}

            <!-- 报告内容 -->
            <el-card class="report-container">
                <!-- 加载状态 -->
                <div class="text-center p-5" v-if="isLoading">
                    <el-skeleton :rows="10" animated />
                </div>

                <!-- 报告内容容器 - 始终存在 -->
                <div class="report-content" id="reportContent" v-show="!isLoading">
                    <!-- 报告将在这里渲染 -->
                </div>
            </el-card>
            
            <!-- 操作按钮 (移到最后) -->
            <el-card class="mb-4 mt-4 operation-card">
                <template #header>
                    <div class="d-flex justify-content-center align-items-center">
                        <div style="color: white; font-weight: 600;">
                            <i class="fas fa-download mr-2"></i> 报告下载
                        </div>
                    </div>
                </template>
                
                <!-- 操作按钮 -->
                <div class="text-center">
                    <button @click="downloadReport('docx')" class="input-btn download-btn">
                        <i class="fas fa-file-word"></i>
                        <span>下载报告 (Word格式)</span>
                    </button>
                </div>
            </el-card>
        </div>
    </div>

    <!-- JavaScript依赖 -->
    <script src="/static/lib/vue/vue.global.prod.js"></script>
    <script src="/static/lib/element-plus/index.js"></script>
    <script src="{{ url_for('static', filename='lib/highlight/highlight.min.js') }}"></script>
    <script src="{{ url_for('static', filename='lib/markdown-it/markdown-it.min.js') }}"></script>
    <script>
        // 确保DOM完全加载后再初始化Vue应用
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 检查Vue是否可用
                if (typeof Vue === 'undefined') {
                    console.error('Vue库未加载，使用降级方案');
                    fallbackReportLoad();
                    return;
                }

                const { createApp, ref, onMounted } = Vue

                // 检查必要的元素是否存在
                const appElement = document.getElementById('app');
                if (!appElement) {
                    console.error('找不到app元素，Vue应用无法挂载');
                    fallbackReportLoad();
                    return;
                }

                createApp({
                delimiters: ['[[', ']]'],  // 使用自定义分隔符，避免与Jinja2冲突
                setup() {
                const isLoading = ref(true)
                const report_time = ref('')
                
                // 返回对话页面
                const goBack = () => {
                    // 保存当前报告数据到localStorage，确保返回后仍可查看
                    try {
                        // 获取当前报告ID
                        let reportId = '{{ report_id }}';
                        const reportData = localStorage.getItem('currentReport');

                        if (reportData) {
                            // 创建一个持久化的备份
                            const backupKey = 'lastViewedReport';
                            localStorage.setItem(backupKey, reportData);

                            // 保存报告ID
                            localStorage.setItem('lastViewedReportId', reportId);
                        }
                    } catch (e) {
                        console.warn('保存报告数据备份失败:', e);
                    }

                    // 返回对话页面
                    window.location.href = '/';
                }
                
                // 下载报告
                const downloadReport = (format) => {
                    // 显示加载状态
                    ElementPlus.ElMessage({
                        message: '正在准备下载...',
                        type: 'info',
                        duration: 2000
                    });

                    // 获取当前报告ID
                    let reportId = '{{ report_id }}';

                    // 如果是动态加载模式，尝试从localStorage获取当前报告ID
                    if (reportId === 'dynamic' || !reportId) {
                        const currentReportData = localStorage.getItem('currentReportData');
                        if (currentReportData) {
                            try {
                                const reportData = JSON.parse(currentReportData);
                                reportId = reportData.report_id || 'dynamic';
                            } catch (e) {
                                console.warn('解析当前报告数据失败:', e);
                                reportId = 'dynamic';
                            }
                        } else {
                            reportId = 'dynamic';
                        }
                    }

                    // 构建下载URL (只支持Word格式)
                    const downloadUrl = '/download_report?format=docx&report_id=' + reportId;
                    console.log('下载报告:', '报告ID:', reportId);

                    // 直接跳转下载
                    window.location.href = downloadUrl;

                    // 显示成功消息
                    setTimeout(() => {
                        ElementPlus.ElMessage({
                            message: '报告下载已开始',
                            type: 'success',
                            duration: 3000
                        });
                    }, 1000);
                }
                
                // 初始化
                onMounted(() => {
                    console.log('Vue应用已挂载，开始初始化报告页面');

                    // 检查关键元素是否存在
                    const reportContent = document.getElementById('reportContent');
                    if (!reportContent) {
                        console.error('reportContent元素不存在，可能存在DOM结构问题');
                        showError('页面结构异常，请刷新页面重试');
                        return;
                    }

                    // 检查是否是动态加载模式
                    const isDynamicLoad = Boolean('{{ dynamic_load|default("") }}');
                    console.log('动态加载模式', isDynamicLoad);

                    if (isDynamicLoad) {
                        loadDynamicReport();
                    } else {
                        loadStaticReport();
                    }
                })

                // 加载动态报告（从localStorage）
                const loadDynamicReport = async () => {
                    try {
                        // 首先尝试从currentReport加载
                        let reportData = localStorage.getItem('currentReport');
                        let timestamp = localStorage.getItem('reportTimestamp');

                        // 如果currentReport不存在，尝试从备份中恢复
                        if (!reportData) {
                            const backupData = localStorage.getItem('lastViewedReport');

                            if (backupData) {
                                localStorage.setItem('currentReport', backupData);
                                reportData = backupData;

                                // 设置时间戳
                                if (!timestamp) {
                                    timestamp = Date.now().toString();
                                    localStorage.setItem('reportTimestamp', timestamp);
                                }
                            }
                        }

                        if (!reportData) {
                            console.error('localStorage中没有找到报告数据');
                            showError('未找到报告数据，请返回重新生成报告');
                            return;
                        }

                        let report;
                        try {
                            report = JSON.parse(reportData);
                        } catch (parseError) {
                            console.error('报告数据解析失败:', parseError);
                            showError('报告数据格式错误，请重新生成报告');
                            return;
                        }

                        // 检查是否是轻量级数据引用，需要从服务端加载完整数据
                        if ((report.type === 'local_file_stored' || report.type === 'server_stored') && report.report_id) {
                            try {
                                showLoading('正在加载完整报告数据...');
                                const fullReport = await loadReportFromLocalFile(report.report_id);
                                if (fullReport && fullReport.report_text) {
                                    report = fullReport;
                                } else {
                                    console.error('从服务端加载的数据不完整或为空');
                                    showError('无法加载完整的报告数据，请重新生成报告');
                                    return;
                                }
                            } catch (error) {
                                console.error('加载完整数据失败:', error);
                                showError('加载报告数据失败，请重新生成报告');
                                return;
                            } finally {
                                hideLoading();
                            }
                        }

                        // 设置报告时间（只显示日期）
                        const now = new Date();
                        report_time.value = now.getFullYear() + '年' +
                                          String(now.getMonth() + 1).padStart(2, '0') + '月' +
                                          String(now.getDate()).padStart(2, '0') + '日';

                        displayDynamicReport(report);

                    } catch (error) {
                        console.error('加载动态报告失败', error);
                        showError('报告数据解析失败，请返回重新生成报告');
                    }
                }

                // 从本地文件加载报告数据
                const loadReportFromLocalFile = async (reportId) => {
                    try {
                        const response = await fetch(`/load_local_report/${reportId}`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            console.log('从本地文件加载报告成功');
                            return result.report_data;
                        } else {
                            console.warn('本地文件加载失败:', response.status);
                            return null;
                        }
                    } catch (error) {
                        console.error('本地文件加载异常:', error);
                        return null;
                    }
                }

                // 加载静态报告（传统模式）
                const loadStaticReport = () => {
                    const reportContent = document.getElementById('reportContent')
                    if (!reportContent) {
                        console.error('找不到报告内容元素')
                        isLoading.value = false
                        return
                    }

                    const md = window.markdownit({
                        html: true,
                        linkify: true,
                        typographer: true,
                        highlight: function (str, lang) {
                            if (lang && hljs.getLanguage(lang)) {
                                try {
                                    return '<pre class="hljs"><code>' +
                                        hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                                        '</code></pre>'
                                } catch (__) { }
                            }
                            return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
                        }
                    })

                    const reportMarkdown = `{{ report_content|safe }}`
                    reportContent.innerHTML = md.render(reportMarkdown)

                    // 初始化代码高亮
                    if (typeof hljs !== 'undefined') {
                        document.querySelectorAll('pre code').forEach((block) => {
                            hljs.highlightElement(block);
                        });
                    } else {
                        console.error('Highlight.js库未加载，代码高亮功能不可用');
                    }

                    isLoading.value = false
                }

                // 显示动态报告
                const displayDynamicReport = (report) => {
                    console.log('开始显示动态报告', report);
                    console.log('报告数据结构检查:');
                    console.log('- report_text:', !!report.report_text, report.report_text ? report.report_text.length + ' 字符' : '无');
                    console.log('- data_summary:', !!report.data_summary);
                    console.log('- visualizations:', !!report.visualizations, report.visualizations ? report.visualizations.length + ' 个' : '无');

                    const reportContent = document.getElementById('reportContent')
                    if (!reportContent) {
                        console.error('找不到reportContent元素');
                        showError('找不到报告内容元素')
                        return
                    }

                    let htmlContent = '';
                    console.log('开始生成HTML内容');

                    // 添加数据概况
                    if (report.data_summary) {
                        htmlContent += generateDataSummaryHTML(report.data_summary);
                    }

                    // 添加报告文本
                    if (report.report_text) {
                        try {
                            // 检查markdownit是否可用
                            if (typeof window.markdownit === 'undefined') {
                                console.warn('markdownit库未加载，使用纯文本显示');
                                htmlContent += `<pre style="white-space: pre-wrap; font-family: inherit;">${report.report_text}</pre>`;
                            } else {
                                const md = window.markdownit({
                                    html: true,
                                    linkify: true,
                                    typographer: true
                                });
                                htmlContent += md.render(report.report_text);
                            }
                        } catch (mdError) {
                            console.error('Markdown渲染失败:', mdError);
                            htmlContent += `<pre style="white-space: pre-wrap; font-family: inherit;">${report.report_text}</pre>`;
                        }
                    }

                    // 添加可视化图表
                    if (report.visualizations && report.visualizations.length > 0) {
                        console.log('添加可视化图表，数量:', report.visualizations.length);
                        htmlContent += generateVisualizationsHTML(report.visualizations);
                    }

                    console.log('生成的HTML内容长度:', htmlContent.length);
                    console.log('HTML内容前200字符:', htmlContent.substring(0, 200));

                    if (htmlContent.trim() === '') {
                        console.warn('生成的HTML内容为空，显示默认消息');
                        htmlContent = `
                            <div class="text-center p-5">
                                <div class="text-warning mb-3">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem;"></i>
                                </div>
                                <h4 class="text-warning">报告内容为空</h4>
                                <p class="text-muted">报告数据中没有找到可显示的内容</p>
                                <button @click="goBack" class="input-btn back-btn">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回重新生成</span>
                                </button>
                            </div>
                        `;
                    }

                    reportContent.innerHTML = htmlContent;
                    console.log('报告内容已设置到DOM');
                    isLoading.value = false;
                    console.log('加载状态已设置为false');
                }

                // 生成数据概况HTML
                const generateDataSummaryHTML = (dataSummary) => {
                    let html = '<div class="mb-4 p-4" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%); color: white; border-radius: 8px;">';
                    html += '<h3><i class="fas fa-database mr-2"></i>数据概况</h3>';
                    html += '<div class="row">';

                    if (dataSummary.total_records) {
                        html += `<div class="col-md-3 col-sm-6 mb-2">
                            <div class="text-center">
                                <div style="font-size: 1.5rem; font-weight: bold;">${dataSummary.total_records.toLocaleString()}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">数据总量</div>
                            </div>
                        </div>`;
                    }

                    if (dataSummary.time_range) {
                        html += `<div class="col-md-3 col-sm-6 mb-2">
                            <div class="text-center">
                                <div style="font-size: 1rem; font-weight: bold;">${dataSummary.time_range}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">时间范围</div>
                            </div>
                        </div>`;
                    }

                    if (dataSummary.data_quality && dataSummary.data_quality.completeness) {
                        html += `<div class="col-md-3 col-sm-6 mb-2">
                            <div class="text-center">
                                <div style="font-size: 1.2rem; font-weight: bold;">${dataSummary.data_quality.completeness}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">数据完整性</div>
                            </div>
                        </div>`;
                    }

                    if (dataSummary.data_quality && dataSummary.data_quality.location_coverage) {
                        html += `<div class="col-md-3 col-sm-6 mb-2">
                            <div class="text-center">
                                <div style="font-size: 0.9rem; font-weight: bold;">${dataSummary.data_quality.location_coverage}</div>
                                <div style="font-size: 0.9rem; opacity: 0.9;">地区覆盖</div>
                            </div>
                        </div>`;
                    }

                    html += '</div></div>';
                    return html;
                }

                // 生成可视化图表HTML
                const generateVisualizationsHTML = (visualizations) => {
                    let html = '<div class="mb-4">';
                    html += '<h2><i class="fas fa-chart-bar mr-2"></i>数据可视化</h2>';

                    visualizations.forEach((viz, index) => {
                        if (viz.image || viz.image_file) {
                            html += `<div class="mb-4 text-center">
                                <h3 class="text-primary">${viz.title || '数据图表'}</h3>`;

                            // 智能图片显示逻辑
                            console.log('处理图表:', viz.title, {
                                hasImage: !!viz.image,
                                hasImageFile: !!viz.image_file,
                                hasImageFilename: !!viz.image_filename,
                                imageStoredSeparately: viz.image_stored_separately
                            });

                            if (viz.image_filename && viz.image_file) {
                                // 有图片文件，优先使用文件路径
                                html += `<img src="/chart_image/${viz.image_filename}" alt="${viz.title || '数据图表'}"
                                             style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
                                             onerror="console.error('图片加载失败:', '${viz.image_filename}'); this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                         <div style="display: none; padding: 20px; background: #f8f9fa; border-radius: 8px; color: #6c757d;">
                                             <i class="fas fa-image"></i> 图片文件加载失败: ${viz.image_filename}
                                         </div>`;
                            } else if (viz.image) {
                                // 使用base64数据
                                html += `<img src="data:image/png;base64,${viz.image}" alt="${viz.title || '数据图表'}"
                                             style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />`;
                            } else {
                                // 没有图片数据，显示占位符
                                console.warn('图表没有图片数据:', viz.title);
                                html += `<div style="padding: 40px; background: #f8f9fa; border-radius: 8px; color: #6c757d; border: 2px dashed #dee2e6;">
                                             <i class="fas fa-chart-bar fa-3x mb-3"></i><br>
                                             <strong>图表数据缺失</strong><br>
                                             <small>标题: ${viz.title || '未知'}</small>
                                         </div>`;
                            }

                            html += `${viz.description ? `<p class="text-muted mt-2"><small>${viz.description}</small></p>` : ''}
                            </div>`;
                        }
                    });

                    html += '</div>';
                    return html;
                }

                // 显示加载状态
                const showLoading = (message = '正在加载...') => {
                    const reportContent = document.getElementById('reportContent')
                    if (reportContent) {
                        reportContent.innerHTML = `
                            <div class="text-center p-5">
                                <div class="mb-3">
                                    <i class="fas fa-spinner fa-spin" style="font-size: 3rem; color: #007bff;"></i>
                                </div>
                                <h4 class="text-primary">加载中</h4>
                                <p class="text-muted">${message}</p>
                            </div>
                        `;
                    }
                    isLoading.value = true;
                }

                // 隐藏加载状态
                const hideLoading = () => {
                    isLoading.value = false;
                }

                // 显示错误
                const showError = (message) => {
                    const reportContent = document.getElementById('reportContent')
                    if (reportContent) {
                        reportContent.innerHTML = `
                            <div class="text-center p-5">
                                <div class="text-danger mb-3">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem;"></i>
                                </div>
                                <h4 class="text-danger">报告加载失败</h4>
                                <p class="text-muted">${message}</p>
                                <button @click="goBack" class="input-btn back-btn">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>返回重新生成</span>
                                </button>
                            </div>
                        `;
                    }
                    isLoading.value = false;
                }

                return {
                    isLoading,
                    goBack,
                    downloadReport,
                    report_time
                }
            }
            }).use(ElementPlus).mount('#app')

            } catch (vueError) {
                console.error('Vue应用初始化失败', vueError);
                fallbackReportLoad();
            }
        }); // 结束 DOMContentLoaded 事件处理器

        // 降级方案：不使用Vue的简单报告加载
        function fallbackReportLoad() {

            // 隐藏加载状态
            const loadingElements = document.querySelectorAll('.el-skeleton');
            loadingElements.forEach(el => el.style.display = 'none');

            // 显示报告内容区域
            const reportContent = document.getElementById('reportContent');
            if (!reportContent) {
                console.error('reportContent元素不存在');
                document.body.innerHTML = '<div style="text-align: center; padding: 50px;"><h2>页面加载失败</h2><p>请刷新页面重试</p></div>';
                return;
            }

            reportContent.style.display = 'block';

            // 尝试加载报告数据
            try {
                const reportData = localStorage.getItem('currentReport');
                if (reportData) {
                    const report = JSON.parse(reportData);

                    let htmlContent = '<div style="padding: 20px;">';

                    // 添加数据概况
                    if (report.data_summary) {
                        htmlContent += '<h3>数据概况</h3>';
                        htmlContent += `<p>总记录数: ${report.data_summary.total_rows || '未知'}</p>`;
                    }

                    // 添加报告文本
                    if (report.report_text) {
                        htmlContent += '<h3>分析报告</h3>';
                        htmlContent += '<div style="white-space: pre-wrap;">' + report.report_text + '</div>';
                    }

                    htmlContent += '</div>';
                    htmlContent += '<div style="text-align: center; margin: 20px;"><button onclick="window.location.href=\'/\'" class="input-btn back-btn" style="display: inline-flex;"><i class="fas fa-arrow-left"></i><span>返回对话</span></button></div>';

                    reportContent.innerHTML = htmlContent;
                } else {
                    reportContent.innerHTML = '<div style="text-align: center; padding: 50px;"><h3>未找到报告数据</h3><p>请返回重新生成报告</p><button onclick="window.location.href=\'/\'" class="input-btn back-btn" style="display: inline-flex;"><i class="fas fa-arrow-left"></i><span>返回对话</span></button></div>';
                }
            } catch (error) {
                console.error('降级方案加载报告失败:', error);
                reportContent.innerHTML = '<div style="text-align: center; padding: 50px;"><h3>报告加载失败</h3><p>请返回重新生成报告</p><button onclick="window.location.href=\'/\'" class="input-btn back-btn" style="display: inline-flex;"><i class="fas fa-arrow-left"></i><span>返回对话</span></button></div>';
            }
        }
    </script>

    {% if has_visualizations %}
    <script>
        // Store visualization data as a JSON string
        var visualizationsDataJSON = '{{ visualizations|tojson|safe }}';
        
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 检查echarts是否可用
                if (typeof echarts === 'undefined') {
                    console.error('ECharts库未加载，可视化功能将不可用');
                    return;
                }
                
                // 解析JSON数据
                try {
                    var visualizationsData = JSON.parse(visualizationsDataJSON);
                    if (!Array.isArray(visualizationsData)) {
                        console.error('可视化数据格式不正确，应为数组');
                        return;
                    }
                    
                    // 渲染所有可视化图表
                    visualizationsData.forEach(function(vizData, index) {
                        try {
                            const vizId = 'viz-' + (index + 1);
                            const chartContainer = document.getElementById(vizId);
                            if (!chartContainer) {
                                console.error('找不到图表容器 ' + vizId);
                                return;
                            }
                            
                            const chart = echarts.init(chartContainer);
                            
                            // 根据图表类型设置选项
                            let option = {};
                            
                            switch(vizData.type) {
                                case 'bar':
                                    option = {
                                        title: { text: vizData.title || '柱状图' },
                                        tooltip: { trigger: 'axis' },
                                        xAxis: { type: 'category', data: vizData.x_data || [] },
                                        yAxis: { type: 'value' },
                                        series: [{
                                            data: vizData.y_data || [],
                                            type: 'bar',
                                            itemStyle: { color: vizData.color || '#409EFF' }
                                        }]
                                    };
                                    break;
                                    
                                case 'line':
                                    option = {
                                        title: { text: vizData.title || '折线图' },
                                        tooltip: { trigger: 'axis' },
                                        xAxis: { type: 'category', data: vizData.x_data || [] },
                                        yAxis: { type: 'value' },
                                        series: [{
                                            data: vizData.y_data || [],
                                            type: 'line',
                                            smooth: vizData.smooth !== false,
                                            itemStyle: { color: vizData.color || '#409EFF' }
                                        }]
                                    };
                                    break;
                                    
                                case 'pie':
                                    option = {
                                        title: { text: vizData.title || '饼图' },
                                        tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                                        series: [{
                                            name: vizData.name || '数据',
                                            type: 'pie',
                                            radius: '60%',
                                            center: ['50%', '50%'],
                                            data: vizData.pie_data || [],
                                            emphasis: {
                                                itemStyle: {
                                                    shadowBlur: 10,
                                                    shadowOffsetX: 0,
                                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                                }
                                            }
                                        }]
                                    };
                                    break;
                                    
                                case 'heatmap':
                                    option = {
                                        title: { text: vizData.title || '热力图' },
                                        tooltip: { position: 'top' },
                                        grid: { height: '50%', top: '10%' },
                                        xAxis: { type: 'category', data: vizData.x_data || [] },
                                        yAxis: { type: 'category', data: vizData.y_data || [] },
                                        visualMap: {
                                            min: vizData.min || 0,
                                            max: vizData.max || 10,
                                            calculable: true,
                                            orient: 'horizontal',
                                            left: 'center',
                                            bottom: '15%'
                                        },
                                        series: [{
                                            type: 'heatmap',
                                            data: vizData.data || [],
                                            label: { show: false }
                                        }]
                                    };
                                    break;
                                    
                                default:
                                    // 如果提供了自定义选项，直接使用
                                    if (vizData.options) {
                                        option = vizData.options;
                                    } else {
                                        throw new Error(`不支持的图表类型: ${vizData.type}`);
                                    }
                            }
                            
                            chart.setOption(option);
                            
                            // 响应窗口大小变化
                            window.addEventListener('resize', function() {
                                chart.resize();
                            });
                        } catch (error) {
                            console.error('渲染图表失败:', error);
                        }
                    });
                } catch (error) {
                    console.error('解析可视化数据失败', error);
                }
            } catch (error) {
                console.error('初始化可视化功能时出错', error);
            }
        });
    </script>
    {% endif %}
</body>

</html>
